package com.newnary.rpa.safebox.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.KeyAgreement;
import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * ECDHE密钥交换算法服务
 */
@Service
@Slf4j
public class ECDHEService {

    private static final String ALGORITHM = "EC";
    private static final String CURVE_NAME = "secp256r1"; // NIST P-256 曲线
    private static final String KEY_AGREEMENT_ALGORITHM = "ECDH";
    
    // 存储临时密钥对的Map，实际应用中应考虑使用更安全的存储方式
    private final Map<String, KeyPair> keyPairMap = new HashMap<>();
    
    /**
     * 生成ECDH密钥对
     * @param clientId 客户端ID
     * @return Base64编码的公钥
     */
    public String generateKeyPair(String clientId) {
        try {
            // 初始化密钥对生成器
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
            ECGenParameterSpec ecSpec = new ECGenParameterSpec(CURVE_NAME);
            keyPairGenerator.initialize(ecSpec, new SecureRandom());
            
            // 生成密钥对
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            
            // 存储密钥对，关联到客户端ID
            keyPairMap.put(clientId, keyPair);
            
            // 获取公钥并编码为Base64
            PublicKey publicKey = keyPair.getPublic();
            String encodedPublicKey = Base64.getEncoder().encodeToString(publicKey.getEncoded());
            
            log.info("Generated key pair for client: {}", clientId);
            
            return encodedPublicKey;
        } catch (Exception e) {
            log.error("Error generating key pair", e);
            throw new RuntimeException("Failed to generate key pair", e);
        }
    }
    
    /**
     * 计算共享密钥
     * @param clientId 客户端ID
     * @param clientPublicKeyBase64 客户端公钥（Base64编码）
     * @return Base64编码的共享密钥
     */
    public String computeSharedSecret(String clientId, String clientPublicKeyBase64) {
        try {
            // 获取之前生成的服务器密钥对
            KeyPair serverKeyPair = keyPairMap.get(clientId);
            if (serverKeyPair == null) {
                throw new IllegalStateException("No key pair found for client: " + clientId);
            }
            
            // 解码客户端公钥
            byte[] clientPublicKeyBytes = Base64.getDecoder().decode(clientPublicKeyBase64);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(clientPublicKeyBytes);
            PublicKey clientPublicKey = keyFactory.generatePublic(keySpec);
            
            // 使用服务器私钥和客户端公钥计算共享密钥
            KeyAgreement keyAgreement = KeyAgreement.getInstance(KEY_AGREEMENT_ALGORITHM);
            keyAgreement.init(serverKeyPair.getPrivate());
            keyAgreement.doPhase(clientPublicKey, true);
            byte[] sharedSecret = keyAgreement.generateSecret();
            
            // 编码共享密钥为Base64
            String sharedSecretBase64 = Base64.getEncoder().encodeToString(sharedSecret);
            
            log.info("Computed shared secret for client: {}", clientId);
            
            return sharedSecretBase64;
        } catch (Exception e) {
            log.error("Error computing shared secret", e);
            throw new RuntimeException("Failed to compute shared secret", e);
        }
    }
    
    /**
     * 清除指定客户端的密钥对
     * @param clientId 客户端ID
     */
    public void clearKeyPair(String clientId) {
        keyPairMap.remove(clientId);
        log.info("Cleared key pair for client: {}", clientId);
    }
    
    /**
     * 检查客户端是否有关联的密钥对
     * @param clientId 客户端ID
     * @return 是否存在密钥对
     */
    public boolean hasKeyPair(String clientId) {
        return keyPairMap.containsKey(clientId);
    }
}