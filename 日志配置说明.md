# Safebox 日志配置说明

## 概述

本项目使用 SLF4J + Logback 作为日志框架，提供了完整的日志记录和管理功能。考虑到这是一个密码管理系统，日志配置特别注重安全性和敏感信息保护。

## 日志框架组成

- **SLF4J**: 日志门面，提供统一的日志API
- **Logback**: 日志实现，Spring Boot 默认集成
- **Lombok @Slf4j**: 简化日志记录器的创建

## 日志配置文件

### 主配置文件
- `src/main/resources/logback-spring.xml`: 主要的日志配置文件
- `src/main/resources/application.yml`: 应用配置中的日志设置

### 环境特定配置
- `src/main/resources/application-dev.yml`: 开发环境日志配置
- `src/main/resources/application-test.yml`: 测试环境日志配置
- `src/main/resources/application-prod.yml`: 生产环境日志配置

## 日志级别配置

### 开发环境 (dev)
- **根日志级别**: DEBUG
- **应用日志级别**: DEBUG
- **Spring框架**: DEBUG
- **第三方库**: INFO

### 测试环境 (test)
- **根日志级别**: INFO
- **应用日志级别**: INFO
- **Spring框架**: INFO
- **第三方库**: WARN

### 生产环境 (prod)
- **根日志级别**: WARN
- **应用日志级别**: INFO（安全相关）
- **Spring框架**: WARN
- **第三方库**: ERROR

## 日志输出配置

### 控制台输出 (CONSOLE)
- **开发/测试环境**: 启用彩色输出，便于调试
- **生产环境**: 可选，简洁格式

### 文件输出

#### 应用日志 (FILE)
- **文件路径**: 
  - 开发/测试: `./logs/safebox.log`
  - 生产: `/var/log/safebox/safebox.log`
- **滚动策略**: 按天滚动，单文件最大100MB
- **保留时间**: 30天
- **总大小限制**: 10GB

#### 安全日志 (SECURITY_FILE)
- **文件路径**: 
  - 开发/测试: `./logs/security.log`
  - 生产: `/var/log/safebox/security.log`
- **滚动策略**: 按天滚动，单文件最大50MB
- **保留时间**: 90天（安全日志保留更长时间）
- **总大小限制**: 5GB

#### 错误日志 (ERROR_FILE)
- **文件路径**: 
  - 开发/测试: `./logs/error.log`
  - 生产: `/var/log/safebox/error.log`
- **级别过滤**: 仅记录ERROR级别日志
- **滚动策略**: 按天滚动，单文件最大50MB
- **保留时间**: 60天
- **总大小限制**: 2GB

## 安全日志记录

### 专门的安全日志记录器
以下类的日志会单独记录到安全日志文件：
- `com.newnary.rpa.safebox.service.SecurityLogService`
- `com.newnary.rpa.safebox.service.SecurityManagerService`
- `com.newnary.rpa.safebox.service.SecurityApplyService`

### 敏感信息保护
项目提供了 `LogUtils` 工具类来处理敏感信息：

#### 脱敏功能
- `maskUsername()`: 用户名脱敏
- `maskIpAddress()`: IP地址脱敏
- `maskSessionId()`: 会话ID脱敏
- `maskApplyItem()`: 申请项脱敏
- `maskUserAgent()`: User Agent脱敏
- `hideSensitiveInfo()`: 完全隐藏敏感信息

#### 安全检查
- `containsSensitiveKeywords()`: 检测敏感关键词
- `safeLogContent()`: 安全地记录可能包含敏感信息的内容

## 异步日志

为了提高性能，文件日志使用异步写入：
- `ASYNC_FILE`: 异步应用日志
- `ASYNC_SECURITY_FILE`: 异步安全日志

配置参数：
- `discardingThreshold`: 0（不丢弃日志）
- `queueSize`: 1024（应用日志）/ 512（安全日志）

## 使用方法

### 在类中使用日志
```java
@Slf4j
@Service
public class YourService {
    
    public void someMethod() {
        log.info("这是一条信息日志");
        log.warn("这是一条警告日志");
        log.error("这是一条错误日志", exception);
    }
}
```

### 记录安全相关日志
```java
@Slf4j
@Service
public class SecurityService {
    
    public void logSecurityEvent(String username, String ip) {
        // 使用脱敏工具
        String maskedUsername = LogUtils.maskUsername(username);
        String maskedIp = LogUtils.maskIpAddress(ip);
        
        log.info("用户登录 - 用户: {}, IP: {}", maskedUsername, maskedIp);
    }
}
```

## 日志监控建议

### 生产环境监控
1. **磁盘空间监控**: 确保日志目录有足够空间
2. **日志文件大小监控**: 监控单个日志文件大小
3. **错误日志监控**: 设置ERROR级别日志告警
4. **安全日志监控**: 监控异常的安全事件

### 日志分析
1. **ELK Stack**: 可以集成Elasticsearch、Logstash、Kibana进行日志分析
2. **日志聚合**: 在分布式环境中聚合日志
3. **安全审计**: 定期审计安全日志

## 配置修改

### 修改日志级别
在对应环境的配置文件中修改：
```yaml
logging:
  level:
    com.newnary.rpa.safebox: DEBUG
```

### 修改日志文件路径
在 `logback-spring.xml` 中修改：
```xml
<property name="LOG_HOME" value="/your/custom/log/path"/>
```

### 修改滚动策略
在 `logback-spring.xml` 中修改对应的 appender 配置。

## 注意事项

1. **敏感信息**: 绝对不要在日志中记录密码、密钥等敏感信息
2. **性能影响**: 过多的DEBUG日志会影响性能，生产环境应使用适当的日志级别
3. **磁盘空间**: 定期清理旧日志文件，避免磁盘空间不足
4. **安全合规**: 安全日志的保留时间应符合相关法规要求
5. **日志轮转**: 确保日志轮转正常工作，避免单个文件过大

## 故障排查

### 日志不输出
1. 检查日志级别配置
2. 检查日志文件路径权限
3. 检查 logback 配置文件语法

### 日志文件过大
1. 检查滚动策略配置
2. 调整日志级别
3. 增加清理策略

### 性能问题
1. 使用异步日志
2. 调整日志级别
3. 优化日志格式
