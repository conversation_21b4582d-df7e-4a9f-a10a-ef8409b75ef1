package com.newnary.rpa.safebox.controller.manager.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据库上传响应DTO
 * 返回数据库上传操作的结果信息
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 */
@Data
public class DatabaseUploadResponse {

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 文件大小（字节）
     */
    private long fileSize;

    /**
     * 密码条目数量
     */
    private int entryCount;

    /**
     * 数据库版本
     */
    private String version;

    /**
     * 加密算法
     */
    private String encryptionAlgorithm;

    /**
     * 密钥派生函数
     */
    private String keyDerivation;

    /**
     * 操作消息
     */
    private String message;
}
