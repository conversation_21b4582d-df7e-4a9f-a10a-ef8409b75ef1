package com.newnary.rpa.safebox.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.newnary.rpa.safebox.common.SecuritySession;
import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.controller.security.request.ApplyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since Created on 2025-09-15
 **/
@Service
@Slf4j
public class SecuritySessionService {

    private final Cache<String, SecuritySession> sessionCache;

    public SecuritySessionService(SafeboxProperties safeboxProperties) {
        sessionCache = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofSeconds(safeboxProperties.getSecurity().getApplySessionTimeout()))
                .maximumSize(safeboxProperties.getSecurity().getApplySessionMaximum())
                .build();
    }

    public SecuritySession createSession(ApplyRequest request) {
        SecuritySession session = SecuritySession.createWith(request.getApplyList());
        sessionCache.put(session.getSessionId(), session);
        return session;
    }

    public Optional<SecuritySession> getSession(String sessionId) {
        return Optional.ofNullable(sessionCache.getIfPresent(sessionId));
    }

    public void removeSession(String sessionId) {
        sessionCache.invalidate(sessionId);
    }

    public void refreshSession(String sessionId) {
        getSession(sessionId).ifPresent(session -> {
            sessionCache.put(session.getSessionId(), session);
        });
    }

}
