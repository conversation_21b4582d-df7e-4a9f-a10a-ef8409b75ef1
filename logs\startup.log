[周五 2025/09/19 18:32:59.67] Safebox启动脚本开始执行 
[周五 2025/09/19 18:32:59.67] 使用JDK路径: D:\safebox\jdk-17.0.10+7 
[周五 2025/09/19 18:32:59.70] 警告: 端口 8080 已被占用，应用可能已在运行 
[周五 2025/09/19 18:32:59.70] 启动命令: "D:\safebox\jdk-17.0.10+7\bin\java.exe" -Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication -Dfile.encoding=UTF-8 -Djava.awt.headless=true -jar "D:\Workspace\IdeaProjects\safebox\target\safebox-0.0.1-SNAPSHOT.jar" --server.port=8080 --spring.profiles.active=prod 
[周五 2025/09/19 18:32:59.71] 正在启动Safebox应用... 
[周五 2025/09/19 18:33:04.23] Safebox应用启动成功，监听端口: 8080 
[周五 2025/09/19 18:33:04.23] 启动脚本执行完成 
[周五 2025/09/19 18:42:27.15] Safebox启动脚本开始执行 
[周五 2025/09/19 18:42:27.15] 使用JDK路径: D:\safebox\jdk-17.0.10+7 
[周五 2025/09/19 18:42:27.18] 警告: 端口 8080 已被占用，应用可能已在运行 
[周五 2025/09/19 18:42:27.18] 切换工作目录到: D:\safebox 
[周五 2025/09/19 18:42:27.18] 工作目录: D:\safebox 
[周五 2025/09/19 18:42:27.18] 启动命令: "D:\safebox\jdk-17.0.10+7\bin\java.exe" -Xms128m -Xmx128m -XX:+UseG1GC -Dfile.encoding=UTF-8 -Djava.awt.headless=true -jar "target\safebox-0.0.1-SNAPSHOT.jar" --server.port=8080 --spring.profiles.active=prod 
[周五 2025/09/19 18:42:27.18] 正在启动Safebox应用... 
[周五 2025/09/19 18:42:32.18] Safebox应用启动成功，监听端口: 8080 
[周五 2025/09/19 18:42:32.18] 启动脚本执行完成 
