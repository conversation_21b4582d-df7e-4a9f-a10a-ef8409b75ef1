package com.newnary.rpa.safebox;

import com.newnary.rpa.safebox.config.SafeboxProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Safebox 主应用类
 * 密码管理系统的启动入口
 *
 * <AUTHOR>
 * @since Created on 2025-09-19
 */
@Slf4j
@SpringBootApplication
@EnableConfigurationProperties({SafeboxProperties.class})
public class SafeboxApplication {

    public static void main(String[] args) {
        log.info("=== Safebox 密码管理系统启动中 ===");
        SpringApplication.run(SafeboxApplication.class, args);
        log.info("=== Safebox 密码管理系统启动完成 ===");
    }

}
