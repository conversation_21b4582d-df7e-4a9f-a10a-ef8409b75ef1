package com.newnary.rpa.safebox.controller.ecdhe;

import com.newnary.rpa.safebox.service.ECDHEService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * ECDH密钥交换算法控制器
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/api/ecdhe")
@Slf4j
public class ECDHEController {
    
    @Resource
    private ECDHEService ecdheService;
    
    /**
     * 生成ECDH密钥对并返回公钥
     * @param request 包含客户端ID的请求
     * @return 包含Base64编码的公钥的响应
     */
    @PostMapping("/generate-keypair")
    public KeyGenerationResponse generateKeyPair(@RequestBody KeyGenerationRequest request) {
        try {
            String clientId = request.getClientId();
            String encodedPublicKey = ecdheService.generateKeyPair(clientId);
            
            // 返回公钥
            KeyGenerationResponse response = new KeyGenerationResponse();
            response.setPublicKey(encodedPublicKey);
            return response;
        } catch (Exception e) {
            log.error("Error generating key pair", e);
            throw new RuntimeException("Failed to generate key pair", e);
        }
    }
    
    /**
     * 接收客户端公钥并计算共享密钥
     * @param request 包含客户端ID和客户端公钥的请求
     * @return 操作状态
     */
    @PostMapping("/compute-shared-secret")
    public SharedSecretResponse computeSharedSecret(@RequestBody SharedSecretRequest request) {
        try {
            String clientId = request.getClientId();
            String clientPublicKeyBase64 = request.getClientPublicKey();
            
            // 使用服务计算共享密钥
            String sharedSecretBase64 = ecdheService.computeSharedSecret(clientId, clientPublicKeyBase64);
            
            // 返回共享密钥（实际应用中应考虑安全性）
            SharedSecretResponse response = new SharedSecretResponse();
            response.setSharedSecret(sharedSecretBase64);
            return response;
        } catch (Exception e) {
            log.error("Error computing shared secret", e);
            throw new RuntimeException("Failed to compute shared secret", e);
        }
    }
    
    /**
     * 清除指定客户端的密钥对
     * @param clientId 客户端ID
     * @return 操作状态
     */
    @DeleteMapping("/keypair/{clientId}")
    public Map<String, String> clearKeyPair(@PathVariable String clientId) {
        ecdheService.clearKeyPair(clientId);
        
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Key pair cleared for client: " + clientId);
        return response;
    }
    
    // 请求和响应数据类
    
    @Data
    public static class KeyGenerationRequest {
        private String clientId;
    }
    
    @Data
    public static class KeyGenerationResponse {
        private String publicKey;
    }
    
    @Data
    public static class SharedSecretRequest {
        private String clientId;
        private String clientPublicKey;
    }
    
    @Data
    public static class SharedSecretResponse {
        private String sharedSecret;
    }
}
