@echo off
:: ========================================
:: Safebox 静默启动脚本 - 专用于开机自启动
:: 完全静默运行，不显示任何窗口
:: ========================================

:: 设置UTF-8编码，避免中文乱码
chcp 65001 >nul 2>&1

:: ========================================
:: 配置区域 - 可根据需要修改以下配置
:: ========================================

:: JDK路径配置 - 请根据实际安装路径修改
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_301"
set "JAVA_HOME_BACKUP=C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot"

:: Spring Boot 环境配置
set "SPRING_PROFILES_ACTIVE=prod"

:: 应用端口配置
set "SERVER_PORT=8080"

:: JVM参数配置
set "JVM_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication"

:: 应用特定参数
set "APP_OPTS=--server.port=%SERVER_PORT% --spring.profiles.active=%SPRING_PROFILES_ACTIVE%"

:: ========================================
:: 静默启动逻辑
:: ========================================

:: 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "JAR_FILE=%SCRIPT_DIR%target\safebox-0.0.1-SNAPSHOT.jar"
set "LOG_DIR=%SCRIPT_DIR%logs"
set "STARTUP_LOG=%LOG_DIR%\startup.log"

:: 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1

:: 记录启动时间
echo [%date% %time%] Safebox静默启动脚本开始执行 >> "%STARTUP_LOG%" 2>&1

:: 检查JDK
if exist "%JAVA_HOME%\bin\java.exe" (
    set "JAVA_EXE=%JAVA_HOME%\bin\java.exe"
    echo [%date% %time%] 使用JDK路径: %JAVA_HOME% >> "%STARTUP_LOG%" 2>&1
) else if exist "%JAVA_HOME_BACKUP%\bin\java.exe" (
    set "JAVA_EXE=%JAVA_HOME_BACKUP%\bin\java.exe"
    echo [%date% %time%] 使用备用JDK路径: %JAVA_HOME_BACKUP% >> "%STARTUP_LOG%" 2>&1
) else (
    echo [%date% %time%] 错误: 未找到Java运行环境 >> "%STARTUP_LOG%" 2>&1
    exit /b 1
)

:: 检查JAR文件
if not exist "%JAR_FILE%" (
    echo [%date% %time%] 错误: 未找到JAR文件: %JAR_FILE% >> "%STARTUP_LOG%" 2>&1
    exit /b 1
)

:: 检查端口占用
netstat -an | find ":%SERVER_PORT%" >nul 2>&1
if %errorlevel% == 0 (
    echo [%date% %time%] 端口 %SERVER_PORT% 已被占用，应用可能已在运行 >> "%STARTUP_LOG%" 2>&1
    exit /b 0
)

:: 构建启动命令
set "FULL_COMMAND="%JAVA_EXE%" %JVM_OPTS% -Dfile.encoding=UTF-8 -Djava.awt.headless=true -jar "%JAR_FILE%" %APP_OPTS%"

:: 记录启动命令
echo [%date% %time%] 启动命令: %FULL_COMMAND% >> "%STARTUP_LOG%" 2>&1

:: 完全静默启动 - 使用wscript实现无窗口启动
echo Set WshShell = CreateObject("WScript.Shell") > "%TEMP%\safebox_start.vbs"
echo WshShell.Run "%FULL_COMMAND%", 0, False >> "%TEMP%\safebox_start.vbs"
cscript //nologo "%TEMP%\safebox_start.vbs" >nul 2>&1
del "%TEMP%\safebox_start.vbs" >nul 2>&1

:: 等待启动
timeout /t 8 /nobreak >nul 2>&1

:: 验证启动结果
netstat -an | find ":%SERVER_PORT%" >nul 2>&1
if %errorlevel% == 0 (
    echo [%date% %time%] Safebox应用静默启动成功，监听端口: %SERVER_PORT% >> "%STARTUP_LOG%" 2>&1
) else (
    echo [%date% %time%] Safebox应用启动失败，请检查日志 >> "%STARTUP_LOG%" 2>&1
)

echo [%date% %time%] 静默启动脚本执行完成 >> "%STARTUP_LOG%" 2>&1
