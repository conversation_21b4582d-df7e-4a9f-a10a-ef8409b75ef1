package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.common.SecurityApplyCard;
import com.newnary.rpa.safebox.common.SecuritySession;
import com.newnary.rpa.safebox.controller.security.request.ApplyRequest;
import com.newnary.rpa.safebox.controller.security.request.ResultRequest;
import com.newnary.rpa.safebox.controller.security.response.AskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Service
@Slf4j
public class SecurityApplyService {

    @Resource
    private SecuritySessionService securitySessionService;
    @Resource
    private LarkInteractionService larkInteractionService;

    public String apply(ApplyRequest request) {
        SecuritySession session = securitySessionService.createSession(request);

        SecurityApplyCard.Params params = SecurityApplyCard.Params.build(
                request.getMessage(),
                request.getApplyList(),
                session.getSessionId()
        );

        try {
            boolean result = larkInteractionService.sendSecurityApplyCard(params);
            if (result) {
                return session.getSessionId();
            }
        } catch (Exception e) {
            log.error("飞书交互卡片发送失败", e);
        }

        securitySessionService.removeSession(session.getSessionId());
        return null;
    }

    public AskResult result(ResultRequest request) {
        AskResult result = new AskResult();

        Optional<SecuritySession> sessionOptional = securitySessionService.getSession(request.getSessionId());
        if (!sessionOptional.isPresent()) {
            result.setAskState(SecuritySession.State.REJECTED.name());
            return result;
        }

        SecuritySession session = sessionOptional.get();
        result.setAskState(session.getState().name());

        if (session.getState() == SecuritySession.State.APPROVED) {
            result.setSecurityList(session.getSecurityList());
        }

        return result;
    }

}
