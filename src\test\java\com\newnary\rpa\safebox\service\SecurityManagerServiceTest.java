package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseMetadata;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseUploadResponse;
import com.newnary.rpa.safebox.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * SecurityManagerService单元测试
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 */
class SecurityManagerServiceTest {

    @Mock
    private SafeboxProperties safeboxProperties;

    @Mock
    private SafeboxProperties.KeePass keepassConfig;

    @InjectMocks
    private SecurityManagerService securityManagerService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 配置mock对象
        when(safeboxProperties.getKeepass()).thenReturn(keepassConfig);
        when(keepassConfig.getKdbxPath()).thenReturn(tempDir.toString());
        when(keepassConfig.getMaxFileSize()).thenReturn(10 * 1024 * 1024L); // 10MB
    }

    @Test
    void testValidateUploadFile_NullFile() {
        // 测试空文件
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.uploadDatabase(null, "password", null);
        });
        assertEquals("请选择要上传的文件", exception.getMessage());
    }

    @Test
    void testValidateUploadFile_EmptyFile() {
        // 测试空文件
        MockMultipartFile emptyFile = new MockMultipartFile("file", "test.kdbx", "application/octet-stream", new byte[0]);

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.uploadDatabase(emptyFile, "password", null);
        });
        assertEquals("请选择要上传的文件", exception.getMessage());
    }

    @Test
    void testValidateUploadFile_WrongExtension() {
        // 测试错误的文件扩展名
        MockMultipartFile wrongFile = new MockMultipartFile("file", "test.txt", "text/plain", "content".getBytes());

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.uploadDatabase(wrongFile, "password", null);
        });
        assertEquals("只允许上传.kdbx格式的KeePass数据库文件", exception.getMessage());
    }

    @Test
    void testValidateUploadFile_TooLarge() {
        // 测试文件过大
        when(keepassConfig.getMaxFileSize()).thenReturn(100L); // 设置很小的限制
        
        MockMultipartFile largeFile = new MockMultipartFile("file", "test.kdbx", "application/octet-stream", 
            "very large content that exceeds the limit".getBytes());
        
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.uploadDatabase(largeFile, "password", null);
        });
        assertTrue(exception.getMessage().contains("文件大小超过限制"));
    }

    @Test
    void testGetDatabaseMetadata_NotExists() {
        // 测试数据库不存在的情况
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.getDatabaseMetadata();
        });
        assertEquals("数据库不存在，请先上传数据库文件", exception.getMessage());
    }

    @Test
    void testDeleteUserDatabase_NotExists() {
        // 测试删除不存在的数据库
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.deleteUserDatabase("password");
        });
        assertEquals("数据库不存在", exception.getMessage());
    }

    @Test
    void testGetAllEntries_NotExists() {
        // 测试获取不存在数据库的条目
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            securityManagerService.getAllEntries("password");
        });
        assertEquals("数据库不存在，请先上传数据库文件", exception.getMessage());
    }

    @Test
    void testCreateUserDirectories() throws IOException {
        // 测试目录创建功能
        // 这个测试需要通过反射或者其他方式来测试私有方法
        // 这里我们通过检查上传过程中是否创建了正确的目录结构来间接测试
        
        Path userDir = tempDir.resolve("default");
        Path originalDir = userDir.resolve("original");
        Path managedDir = userDir.resolve("managed");
        
        // 验证目录不存在
        assertFalse(Files.exists(userDir));
        assertFalse(Files.exists(originalDir));
        assertFalse(Files.exists(managedDir));
        
        // 注意：由于uploadDatabase方法会验证KeePass文件格式，这里无法直接测试目录创建
        // 在实际项目中，应该将目录创建逻辑提取为包级别可见的方法以便测试
    }
}
