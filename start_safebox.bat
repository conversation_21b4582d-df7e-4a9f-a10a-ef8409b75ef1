@echo off
:: ========================================
:: Safebox 密码管理系统启动脚本
:: 支持开机自启动和静默运行
:: ========================================

:: 设置UTF-8编码，避免中文乱码
chcp 65001 >nul 2>&1

:: ========================================
:: 配置区域 - 可根据需要修改以下配置
:: ========================================

:: JDK路径配置 - 请根据实际安装路径修改
:: 如果系统已配置JAVA_HOME环境变量，可以使用 %JAVA_HOME%
:: 否则请指定完整的JDK安装路径
set "JAVA_HOME=D:\safebox\jdk-17.0.10+7"

:: Jar包所在目录
set "JAR_DIR=D:\safebox"
set "JAR_FILE=%JAR_DIR%\safebox.jar"

:: Spring Boot 环境配置
:: 可选值: dev, test, prod
set "SPRING_PROFILES_ACTIVE=prod"

:: 应用端口配置
set "SERVER_PORT=8080"

:: JVM参数配置
set "JVM_OPTS=-Xms128m -Xmx128m -XX:+UseG1GC"

:: 应用特定参数
set "APP_OPTS=--server.port=%SERVER_PORT% --spring.profiles.active=%SPRING_PROFILES_ACTIVE%"

:: 日志文件路径
set "LOG_DIR=%JAR_DIR%\logs"
set "STARTUP_LOG=%LOG_DIR%\startup.log"

:: ========================================
:: 脚本执行区域 - 一般不需要修改
:: ========================================

:: 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: 记录启动时间
echo [%date% %time%] Safebox启动脚本开始执行 >> "%STARTUP_LOG%"

:: 检查JDK是否存在
if exist "%JAVA_HOME%\bin\java.exe" (
    set "JAVA_EXE=%JAVA_HOME%\bin\java.exe"
    echo [%date% %time%] 使用JDK路径: %JAVA_HOME% >> "%STARTUP_LOG%"
) else (
    echo [%date% %time%] 错误: 未找到Java运行环境，请检查JDK路径配置 >> "%STARTUP_LOG%"
    echo 错误: 未找到Java运行环境，请检查JDK路径配置
    echo 请编辑脚本中的JAVA_HOME变量，设置正确的JDK安装路径
    pause
    exit /b 1
)

:: 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo [%date% %time%] 错误: 未找到应用JAR文件: %JAR_FILE% >> "%STARTUP_LOG%"
    echo 错误: 未找到应用JAR文件
    echo 请确保已执行 mvn clean package 构建项目
    pause
    exit /b 1
)

:: 检查端口是否被占用
netstat -an | find ":%SERVER_PORT%" >nul 2>&1
if %errorlevel% == 0 (
    echo [%date% %time%] 警告: 端口 %SERVER_PORT% 已被占用，应用可能已在运行 >> "%STARTUP_LOG%"
    echo 警告: 端口 %SERVER_PORT% 已被占用，应用可能已在运行
)

:: 切换到JAR包所在目录，确保应用以正确的工作目录运行
echo [%date% %time%] 切换工作目录到: %JAR_DIR% >> "%STARTUP_LOG%"
cd /d "%JAR_DIR%"

:: 构建完整的启动命令
set FULL_COMMAND="%JAVA_EXE%" %JVM_OPTS% -Dfile.encoding=UTF-8 -Djava.awt.headless=true -jar "%JAR_FILE%" %APP_OPTS%

:: 记录启动命令和工作目录
echo [%date% %time%] 工作目录: %CD% >> "%STARTUP_LOG%"
echo [%date% %time%] 启动命令: %FULL_COMMAND% >> "%STARTUP_LOG%"

:: 静默启动应用
:: /min: 最小化窗口启动
:: /b: 在后台启动，不创建新的命令提示符窗口
:: /d: 指定启动目录
echo [%date% %time%] 正在启动Safebox应用... >> "%STARTUP_LOG%"
start "Safebox密码管理系统" /min /b /d "%JAR_DIR%" %FULL_COMMAND%

:: 等待几秒钟让应用启动
timeout /t 5 /nobreak >nul

:: 检查应用是否成功启动
netstat -an | find ":%SERVER_PORT%" >nul 2>&1
if %errorlevel% == 0 (
    echo [%date% %time%] Safebox应用启动成功，监听端口: %SERVER_PORT% >> "%STARTUP_LOG%"
    echo Safebox应用启动成功，监听端口: %SERVER_PORT%
    echo 访问地址: http://localhost:%SERVER_PORT%
) else (
    echo [%date% %time%] Safebox应用启动可能失败，请检查日志文件 >> "%STARTUP_LOG%"
    echo Safebox应用启动可能失败，请检查日志文件
    echo 日志位置: %LOG_DIR%
)

echo [%date% %time%] 启动脚本执行完成 >> "%STARTUP_LOG%"
echo.
echo 脚本执行完成。如需查看详细日志，请查看: %STARTUP_LOG%
echo 应用日志位置: %LOG_DIR%\safebox.log

:: 如果不是静默模式，暂停显示结果
if not "%1"=="silent" (
    echo.
    echo 按任意键退出...
    pause >nul
)