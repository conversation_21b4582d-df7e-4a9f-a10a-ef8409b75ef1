package com.newnary.rpa.safebox.controller.security;

import com.newnary.rpa.safebox.common.BaseResponse;
import com.newnary.rpa.safebox.controller.security.request.ApplyRequest;
import com.newnary.rpa.safebox.controller.security.request.ResultRequest;
import com.newnary.rpa.safebox.controller.security.response.AskResult;
import com.newnary.rpa.safebox.service.SecurityApplyService;
import com.newnary.rpa.safebox.service.SecurityLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 客户端请求密码
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/security-apply")
public class SecurityApplyController {

    @Resource
    private SecurityApplyService securityApplyService;
    
    @Resource
    private SecurityLogService securityLogService;

    /**
     * 申请密码
     * 1. 客户端发起密码申请
     * 2. 返回本次密码申请的查询session
     **/
    @PostMapping("apply")
    public BaseResponse<String> apply(@RequestBody ApplyRequest request, HttpServletRequest httpRequest) {
        String session = securityApplyService.apply(request);
        
        // 记录审计日志
        securityLogService.logSecurityApply(request, session, httpRequest);
        
        if (session == null) {
            return BaseResponse.fail("服务器异常");
        }
        return BaseResponse.success(session);
    }

    /**
     * 获取结果
     * 1. 根据session查询结果
     **/
    @PostMapping("result")
    public BaseResponse<AskResult> result(@RequestBody ResultRequest request, HttpServletRequest httpRequest) {
        AskResult result = securityApplyService.result(request);

        // 记录审计日志
        securityLogService.logSecurityResult(request, httpRequest);
        
        return BaseResponse.success(result);
    }

}
