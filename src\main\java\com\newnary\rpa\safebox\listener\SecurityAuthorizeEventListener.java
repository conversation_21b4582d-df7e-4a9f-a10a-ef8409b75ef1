package com.newnary.rpa.safebox.listener;

import com.newnary.rpa.safebox.common.SecurityApplyCard;
import com.newnary.rpa.safebox.common.SecurityEntry;
import com.newnary.rpa.safebox.common.SecuritySession;
import com.newnary.rpa.safebox.event.SecurityAuthorizeEventEvent;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import com.newnary.rpa.safebox.service.SecuritySessionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since Created on 2025-09-15
 **/
@Component
@Slf4j
public class SecurityAuthorizeEventListener {

    @Resource
    private SecuritySessionService securitySessionService;
    @Resource
    private SecurityManagerService securityManagerService;

    @EventListener
    public void handleApproved(SecurityAuthorizeEventEvent event) {
        SecurityApplyCard applyCard = event.getSource();
        Optional<SecuritySession> sessionOptional = securitySessionService.getSession(applyCard.getTemplateParams().getSessionId());

        // 超时
        if (!sessionOptional.isPresent()) {
            applyCard.action(true, "申请会话已过期!");
            return;
        }

        SecuritySession securitySession = sessionOptional.get();

        if ("approve".equals(applyCard.getAction())) {
            try {
                if (StringUtils.isEmpty(applyCard.getMasterPassword())) {
                    applyCard.action(false, "密码错误, 数据库解锁失败!");
                }

                // 获取密码条目
                List<SecurityEntry> securityEntries = securityManagerService.searchEntriesByTitle(applyCard.getMasterPassword(), securitySession.getApplyList());
                securitySession.approved(securityEntries);
                securitySessionService.refreshSession(securitySession.getSessionId());
                applyCard.action(true, "授权成功");
            } catch (Exception e) {
                applyCard.action(false, "密码错误, 数据库解锁失败!");
            }
        } else if ("reject".equals(applyCard.getAction())) {
            securitySession.rejected();
            applyCard.action(true, "申请已拒绝");
        } else {
            throw new RuntimeException("未知的Action");
        }
    }

}
