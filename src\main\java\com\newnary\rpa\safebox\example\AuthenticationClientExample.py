#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API鉴权客户端示例 - Python实现
用于生成X-Nonce、X-Timestamp和Authorization token

基于HmacSHA256签名的API鉴权机制
签名算法: HMAC-SHA256(nonce + timestamp, accessPassword)

作者: yangzc
创建时间: 2025-09-18
"""

import hmac
import hashlib
import base64
import secrets
import time
import requests
import json
from typing import Dict


class AuthenticationClient:
    """API鉴权客户端"""

    def __init__(self, access_password: str, base_url: str = "http://localhost:8080"):
        """
        初始化鉴权客户端

        Args:
            access_password: API访问密码
            base_url: API服务器基础URL
        """
        self.access_password = access_password
        self.base_url = base_url.rstrip('/')

    def generate_nonce(self, length: int = 24) -> str:
        """
        生成随机nonce

        Args:
            length: nonce字节长度，默认24字节

        Returns:
            Base64 URL安全编码的nonce字符串
        """
        # 生成随机字节
        random_bytes = secrets.token_bytes(length)
        # Base64 URL安全编码，移除填充字符
        nonce = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')
        return nonce

    def generate_timestamp(self) -> str:
        """
        生成当前时间戳（毫秒）

        Returns:
            毫秒时间戳字符串
        """
        return str(int(time.time() * 1000))

    def generate_signature(self, nonce: str, timestamp: str) -> str:
        """
        生成HMAC-SHA256签名

        Args:
            nonce: 随机数
            timestamp: 时间戳

        Returns:
            Base64编码的签名字符串
        """
        # 签名数据: nonce + timestamp
        data = nonce + timestamp

        # 使用HMAC-SHA256算法签名
        signature = hmac.new(
            self.access_password.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).digest()

        # Base64编码
        return base64.b64encode(signature).decode('utf-8')

    def generate_auth_headers(self) -> Dict[str, str]:
        """
        生成完整的鉴权请求头

        Returns:
            包含Authorization、X-Nonce、X-Timestamp的字典
        """
        nonce = self.generate_nonce()
        timestamp = self.generate_timestamp()
        signature = self.generate_signature(nonce, timestamp)

        return {
            'Authorization': signature,
            'X-Nonce': nonce,
            'X-Timestamp': timestamp,
            'Content-Type': 'application/json'
        }

    def make_authenticated_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        发送带鉴权的HTTP请求

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint: API端点路径
            **kwargs: requests库的其他参数

        Returns:
            HTTP响应对象
        """
        # 生成鉴权头
        auth_headers = self.generate_auth_headers()

        # 合并请求头
        headers = kwargs.get('headers', {})
        headers.update(auth_headers)
        kwargs['headers'] = headers

        # 构建完整URL
        url = f"{self.base_url}{endpoint}"

        # 发送请求
        return requests.request(method, url, **kwargs)

    def test_authentication(self) -> bool:
        """
        测试鉴权是否正常工作

        Returns:
            True表示鉴权成功，False表示失败
        """
        try:
            response = self.make_authenticated_request('GET', '/security-manager/metadata')
            return response.status_code == 200
        except Exception as e:
            print(f"鉴权测试失败: {e}")
            return False


def demo_basic_usage():
    """基础使用示例"""
    print("=== API鉴权客户端示例 - Python实现 ===\n")

    # 初始化客户端
    access_password = "kS2Vmf"  # 替换为实际的API访问密码
    client = AuthenticationClient(access_password)

    print("1. 生成鉴权信息:")
    auth_headers = client.generate_auth_headers()
    for key, value in auth_headers.items():
        print(f"   {key}: {value}")
    print()

    print("2. 测试API调用:")
    try:
        # 调用数据库元数据API
        response = client.make_authenticated_request('GET', '/security-manager/metadata')
        print(f"   响应状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print("   ✅ API调用成功!")
        else:
            print(f"   响应内容: {response.text}")
            print("   ❌ API调用失败")

    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")


def demo_multiple_requests():
    """多次请求示例（演示防重放机制）"""
    print("\n=== 多次请求示例 ===\n")

    access_password = "kS2Vmf"
    client = AuthenticationClient(access_password)

    for i in range(3):
        print(f"第{i+1}次请求:")
        auth_headers = client.generate_auth_headers()
        print(f"   Nonce: {auth_headers['X-Nonce']}")
        print(f"   Timestamp: {auth_headers['X-Timestamp']}")
        print(f"   Signature: {auth_headers['Authorization']}")

        try:
            response = client.make_authenticated_request('GET', '/security-manager/metadata')
            if response.status_code == 200:
                print("   ✅ 请求成功")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

        print()
        time.sleep(0.1)  # 短暂延迟


def demo_manual_generation():
    """手动生成鉴权信息示例"""
    print("\n=== 手动生成鉴权信息示例 ===\n")

    access_password = "kS2Vmf"
    client = AuthenticationClient(access_password)

    # 手动生成各个组件
    nonce = client.generate_nonce()
    timestamp = client.generate_timestamp()
    signature = client.generate_signature(nonce, timestamp)

    print("手动生成的鉴权信息:")
    print(f"   Access Password: {access_password}")
    print(f"   Nonce: {nonce}")
    print(f"   Timestamp: {timestamp}")
    print(f"   Signature Data: {nonce + timestamp}")
    print(f"   Authorization: {signature}")
    print()

    # 验证签名
    print("验证签名:")
    expected_signature = client.generate_signature(nonce, timestamp)
    if signature == expected_signature:
        print("   ✅ 签名验证成功")
    else:
        print("   ❌ 签名验证失败")


def demo_curl_command():
    """生成curl命令示例"""
    print("\n=== 生成curl命令示例 ===\n")

    access_password = "kS2Vmf"
    client = AuthenticationClient(access_password)

    auth_headers = client.generate_auth_headers()

    curl_command = f"""curl -X GET \\
  -H "Content-Type: application/json" \\
  -H "Authorization: {auth_headers['Authorization']}" \\
  -H "X-Nonce: {auth_headers['X-Nonce']}" \\
  -H "X-Timestamp: {auth_headers['X-Timestamp']}" \\
  "http://localhost:8080/security-manager/metadata\""""

    print("生成的curl命令:")
    print(curl_command)


if __name__ == "__main__":
    # 运行所有示例
    demo_basic_usage()
    demo_multiple_requests()
    demo_manual_generation()
    demo_curl_command()

    print("\n=== 使用说明 ===")
    print("1. 确保服务器正在运行 (http://localhost:8080)")
    print("2. 修改access_password为正确的API访问密码")
    print("3. 安装依赖: pip install requests")
    print("4. 运行脚本: python AuthenticationClientExample.py")
    print("\n=== 核心算法 ===")
    print("1. 生成24字节随机nonce，Base64 URL安全编码")
    print("2. 生成毫秒时间戳")
    print("3. 签名数据 = nonce + timestamp")
    print("4. 使用HMAC-SHA256(签名数据, 访问密码)生成签名")
    print("5. 将签名进行Base64编码作为Authorization头")