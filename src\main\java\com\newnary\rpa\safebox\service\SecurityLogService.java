package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.common.SecurityEntryWrap;
import com.newnary.rpa.safebox.common.SecuritySession;
import com.newnary.rpa.safebox.controller.security.request.ApplyRequest;
import com.newnary.rpa.safebox.controller.security.request.ResultRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 统一日志记录
 *
 * <AUTHOR>
 * @since Created on 2025-09-17
 **/
@Service
@Slf4j
public class SecurityLogService {

    @Resource
    private SecuritySessionService securitySessionService;

    /**
     * 记录密码申请日志
     *
     * @param request     申请请求
     * @param sessionId   会话ID
     * @param httpRequest httpRequest
     */
    public void logSecurityApply(ApplyRequest request, String sessionId, HttpServletRequest httpRequest) {
        String userAgent = getUserAgent(httpRequest);
        String clientIp = getClientIp(httpRequest);

        if (sessionId == null) {
            log.info("密码申请(异常) - 客户端IP: {}, UA: {}, 申请说明: {}, 申请项: {}",
                    clientIp,
                    userAgent,
                    request.getMessage(),
                    String.join(", ", request.getApplyList()));
        } else {
            log.info("密码申请 - sessionId: {}, 客户端IP: {}, UA: {}, 申请说明: {}, 申请项: {}",
                    sessionId,
                    clientIp,
                    userAgent,
                    request.getMessage(),
                    String.join(", ", request.getApplyList()));
        }
    }

    /**
     * 记录获取密码结果日志
     * 仅在授权后才记录日志
     *
     * @param request     结果请求
     * @param httpRequest httpRequest
     */
    public void logSecurityResult(ResultRequest request, HttpServletRequest httpRequest) {
        Optional<SecuritySession> sessionOptional = securitySessionService.getSession(request.getSessionId());

        if (!sessionOptional.isPresent()) {
            String userAgent = getUserAgent(httpRequest);
            String clientIp = getClientIp(httpRequest);

            log.info("尝试通过不存在会话获取密码授权结果 - 会话ID: {}, 客户端IP: {}, UA: {}",
                    request.getSessionId(),
                    clientIp,
                    userAgent);
            return;
        }

        SecuritySession session = sessionOptional.get();
        if (SecuritySession.State.WAIT != session.getState()) {
            String userAgent = getUserAgent(httpRequest);
            String clientIp = getClientIp(httpRequest);

            String successItems = session.getSecurityList().stream()
                    .filter(warp -> "success".equals(warp.getState()))
                    .map(SecurityEntryWrap::getApplyKey)
                    .collect(Collectors.joining(", "));

            log.info("获取密码授权结果 - 授权状态: {}, 会话ID: {}, 客户端IP: {}, UA: {}, 解密成功项: {}",
                    session.getState(),
                    session.getSessionId(),
                    clientIp,
                    userAgent,
                    successItems);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }

    /**
     * 获取UA
     **/
    private String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

}
