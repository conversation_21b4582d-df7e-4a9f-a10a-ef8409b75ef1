package com.newnary.rpa.safebox.config;

import com.newnary.rpa.safebox.filter.AuthenticationFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 过滤器配置类
 * 
 * <AUTHOR>
 * @since Created on 2025-09-18
 */
@Configuration
public class FilterConfig {

    @Resource
    private AuthenticationFilter authenticationFilter;

    /**
     * 注册鉴权过滤器
     * 
     * @return 过滤器注册Bean
     */
    @Bean
    public FilterRegistrationBean<AuthenticationFilter> authenticationFilterRegistration() {
        FilterRegistrationBean<AuthenticationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(authenticationFilter);
        
        // 设置过滤器应用的URL模式
        registration.addUrlPatterns("/api/*", "/security-apply/*", "/security-manager/*");
        
        // 设置过滤器顺序
        registration.setOrder(1);
        
        // 设置过滤器名称
        registration.setName("authenticationFilter");
        
        return registration;
    }
}
