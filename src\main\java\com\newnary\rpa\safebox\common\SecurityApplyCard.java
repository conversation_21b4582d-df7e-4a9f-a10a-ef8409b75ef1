package com.newnary.rpa.safebox.common;

import com.lark.oapi.event.cardcallback.model.CallBackToast;
import com.lark.oapi.event.cardcallback.model.P2CardActionTrigger;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since Created on 2025-09-16
 **/
@Data
public class SecurityApplyCard {

    private static final String titleColor_red = "red";
    private static final String titleColor_green = "green";
    private static final String titleColor_grey = "grey";

    /**
     * 卡片变量
     **/
    private Params templateParams;

    /**
     * 动作: approve / reject
     **/
    private String action;

    /**
     * 主密码
     **/
    private String masterPassword;

    /**
     * 验证结果
     **/
    private Boolean authResult;

    /**
     * 回复浮窗消息
     **/
    private String replyToast;

    @Data
    public static class Params {
        private String titleColor;
        private String applyReason;
        private String applyList;
        private String applyTime;
        private String sessionId;
        private Boolean cardClose;

        public static Params build(String applyReason, List<String> applyList, String sessionId) {
            Params params = new Params();
            params.setTitleColor(titleColor_red);
            params.setApplyReason(applyReason);
            params.setApplyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            params.setSessionId(sessionId);
            params.setCardClose(false);

            if (applyList.size() <= 5) {
                // 小于5行, 直接换行
                params.setApplyList(String.join("\n", applyList));
            } else {
                StringBuilder applyListSb = new StringBuilder();
                // 大于5行, 使用','号分隔, 每3个元素为一行
                for (int i = 0; i < applyList.size(); i++) {
                    applyListSb.append(applyList.get(i));
                    if (i < applyList.size() - 1) {
                        applyListSb.append(", ");
                    }
                    if ((i + 1) % 3 == 0) {
                        applyListSb.append("\n");
                    }
                }
                params.setApplyList(applyListSb.toString());
            }

            return params;
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("titleColor", titleColor);
            map.put("applyReason", applyReason);
            map.put("applyList", applyList);
            map.put("applyTime", applyTime);
            map.put("sessionId", sessionId);
            map.put("cardClose", cardClose);
            return map;
        }
    }

    public static SecurityApplyCard createWith(P2CardActionTrigger event) {
        Map<String, Object> actionValues = event.getEvent().getAction().getValue();
        String applyReason = (String) actionValues.get("applyReason");
        String applyList = (String) actionValues.get("applyList");
        String applyTime = (String) actionValues.get("applyTime");
        String sessionId = (String) actionValues.get("sessionId");
        String action = (String) actionValues.get("action");
        String masterPassword = null;
        if ("approve".equals(action)) {
            masterPassword = (String) event.getEvent().getAction().getFormValue().get("masterPwd");
        }

        Params params = new Params();
        params.setTitleColor(titleColor_red);
        params.setApplyReason(applyReason);
        params.setApplyList(applyList);
        params.setApplyTime(applyTime);
        params.setSessionId(sessionId);
        params.setCardClose(false);

        SecurityApplyCard card = new SecurityApplyCard();
        card.setTemplateParams(params);
        card.setAction(action);
        card.setMasterPassword(masterPassword);

        return card;
    }

    public void action(boolean authResult, String replyToast) {
        setAuthResult(authResult);
        setReplyToast(replyToast);

        if (authResult) {
            templateParams.setCardClose(true);

            if ("approve".equals(action)) {
                templateParams.setTitleColor(titleColor_green);
            } else if ("reject".equals(action)) {
                templateParams.setTitleColor(titleColor_grey);
            } else {
                throw new RuntimeException("未知的Action");
            }
        }
    }

    public CallBackToast getToast() {
        CallBackToast toast = new CallBackToast();
        toast.setContent(getReplyToast());
        //弹窗提示的类型。可选值有：info、success、error、和 warning。
        if (Boolean.FALSE.equals(authResult)) {
            toast.setType("error");
        } else if ("approve".equals(action)) {
            toast.setType("success");
        } else if ("reject".equals(action)) {
            toast.setType("warning");
        } else {
            throw new RuntimeException("未知的Action");
        }
        return toast;
    }

    private void setTemplateParams(Params templateParams) {
        this.templateParams = templateParams;
    }

    private void setAction(String action) {
        this.action = action;
    }

    private void setMasterPassword(String masterPassword) {
        this.masterPassword = masterPassword;
    }

}
