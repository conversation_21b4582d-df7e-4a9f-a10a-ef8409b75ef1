package com.newnary.rpa.safebox.common;

import lombok.Data;

/**
 * 密码
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@Data
public class SecurityEntryWrap {

    /**
     * 申请项
     **/
    private String applyKey;

    /**
     * 是否成功 成功 = success
     **/
    private String state;

    /**
     * 密码条目
     **/
    private SecurityEntry entry;

    public SecurityEntryWrap() {
    }

    public SecurityEntryWrap(String applyKey) {
        this.applyKey = applyKey;
        this.state = "fail";
    }

}
