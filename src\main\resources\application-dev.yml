# 开发环境配置
logging:
  level:
    root: INFO
    com.newnary.rpa.safebox: INFO
    # 开发环境下可以看到更多Spring框架的日志
    org.springframework.web: INFO
    org.springframework.security: INFO
    # HTTP请求详细日志
    org.springframework.web.servlet.DispatcherServlet: INFO
    # 第三方库保持INFO级别
    org.linguafranca.pwdb: INFO
    com.larksuite.oapi: INFO
  file:
    name: ./logs/safebox-dev.log

# 开发环境特定配置
server:
  port: 8080

spring:
  thymeleaf:
    cache: false  # 开发环境禁用模板缓存
  web:
    resources:
      cache:
        period: 0  # 开发环境禁用静态资源缓存

safebox:
  accessPassword: kS2Vmf # 接口访问密码