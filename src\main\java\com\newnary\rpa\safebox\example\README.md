# API鉴权客户端示例

本目录包含了不同编程语言的API鉴权客户端实现示例。

## Python实现

### 文件说明
- `AuthenticationClientExample.py` - Python版本的API鉴权客户端

### 功能特性
- ✅ 生成随机nonce（24字节，Base64 URL安全编码）
- ✅ 生成毫秒时间戳
- ✅ 计算HMAC-SHA256签名
- ✅ 自动生成完整的鉴权请求头
- ✅ 封装的HTTP请求方法
- ✅ 多种使用示例

### 使用方法

#### 1. 安装依赖
```bash
pip install requests
```

#### 2. 运行示例
```bash
python AuthenticationClientExample.py
```

#### 3. 代码示例
```python
from AuthenticationClientExample import AuthenticationClient

# 初始化客户端
client = AuthenticationClient("kS2Vmf")  # 替换为实际密码

# 生成鉴权头
auth_headers = client.generate_auth_headers()
print(auth_headers)

# 发送鉴权请求
response = client.make_authenticated_request('GET', '/security-manager/metadata')
print(response.json())
```

### 核心算法

1. **生成Nonce**: 使用`secrets.token_bytes(24)`生成24字节随机数，然后Base64 URL安全编码
2. **生成时间戳**: 使用`int(time.time() * 1000)`获取毫秒时间戳
3. **计算签名**: 
   - 签名数据 = nonce + timestamp
   - 使用HMAC-SHA256(签名数据, 访问密码)生成签名
   - 将签名进行Base64编码

### 输出示例

运行脚本后会看到类似以下输出：

```
=== API鉴权客户端示例 - Python实现 ===

1. 生成鉴权信息:
   Authorization: gB90cgYrEM5qUfn/7SXDDD5KaYMEql9XaM7e7d9No3U=
   X-Nonce: IVizyCU4Iqi4C6GnUXjE1hvOWQbspPBu
   X-Timestamp: 1758182270274
   Content-Type: application/json

2. 测试API调用:
   响应状态码: 200
   ✅ API调用成功!
```

## Java实现

### 文件说明
- `AuthenticationClientExample.java` - Java版本的API鉴权客户端

### 使用方法
```bash
javac AuthenticationClientExample.java
java AuthenticationClientExample
```

## 注意事项

1. **密码安全**: 请妥善保管API访问密码，不要在代码中硬编码
2. **时间同步**: 确保客户端和服务端时间同步，避免时间戳验证失败
3. **网络连接**: 确保能够访问API服务器地址
4. **错误处理**: 生产环境中请添加适当的错误处理逻辑

## 支持的API端点

- `GET /security-manager/metadata` - 获取数据库元数据
- `POST /security-manager/getAllEntries` - 获取所有条目
- `DELETE /security-manager/delete` - 删除数据库
- `POST /security-apply/apply` - 申请密码
- `POST /security-apply/result` - 获取申请结果

## 故障排除

### 常见错误

1. **401 Unauthorized**: 检查访问密码是否正确
2. **Connection Error**: 检查服务器是否运行在正确端口
3. **Signature Mismatch**: 检查签名算法实现是否正确
4. **Timestamp Error**: 检查客户端和服务端时间是否同步

### 调试技巧

1. 打印生成的鉴权头信息
2. 检查服务端日志中的鉴权验证信息
3. 使用curl命令验证生成的签名是否正确
