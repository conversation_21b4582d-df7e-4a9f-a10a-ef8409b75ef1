package com.newnary.rpa.safebox.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 日志工具类
 * 提供敏感信息脱敏和安全日志记录功能
 *
 * <AUTHOR>
 * @since Created on 2025-09-19
 */
public class LogUtils {

    /**
     * 脱敏用户名
     * 保留前2位和后1位，中间用*替代
     *
     * @param username 用户名
     * @return 脱敏后的用户名
     */
    public static String maskUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return "***";
        }
        
        if (username.length() <= 3) {
            return "***";
        }
        
        if (username.length() <= 6) {
            return username.substring(0, 1) + "***" + username.substring(username.length() - 1);
        }
        
        return username.substring(0, 2) + "***" + username.substring(username.length() - 1);
    }

    /**
     * 脱敏IP地址
     * 保留前两段，后两段用*替代
     *
     * @param ip IP地址
     * @return 脱敏后的IP地址
     */
    public static String maskIpAddress(String ip) {
        if (StringUtils.isBlank(ip)) {
            return "***.***.***.***";
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return "***.***.***.***";
        }
        
        return parts[0] + "." + parts[1] + ".***." + "***";
    }

    /**
     * 脱敏会话ID
     * 保留前4位和后4位，中间用*替代
     *
     * @param sessionId 会话ID
     * @return 脱敏后的会话ID
     */
    public static String maskSessionId(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            return "****-****-****";
        }
        
        if (sessionId.length() <= 8) {
            return "****-****-****";
        }
        
        return sessionId.substring(0, 4) + "-****-" + sessionId.substring(sessionId.length() - 4);
    }

    /**
     * 脱敏密码申请项
     * 只保留前3个字符，其余用*替代
     *
     * @param applyItem 申请项
     * @return 脱敏后的申请项
     */
    public static String maskApplyItem(String applyItem) {
        if (StringUtils.isBlank(applyItem)) {
            return "***";
        }
        
        if (applyItem.length() <= 3) {
            return "***";
        }
        
        return applyItem.substring(0, 3) + "***";
    }

    /**
     * 脱敏User Agent
     * 保留浏览器类型信息，隐藏详细版本信息
     *
     * @param userAgent User Agent字符串
     * @return 脱敏后的User Agent
     */
    public static String maskUserAgent(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            return "Unknown/***";
        }
        
        // 简化User Agent，只保留主要浏览器信息
        if (userAgent.contains("Chrome")) {
            return "Chrome/***";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox/***";
        } else if (userAgent.contains("Safari")) {
            return "Safari/***";
        } else if (userAgent.contains("Edge")) {
            return "Edge/***";
        } else {
            return "Unknown/***";
        }
    }

    /**
     * 完全隐藏敏感信息
     * 用于密码、密钥等绝对不能泄露的信息
     *
     * @param sensitiveInfo 敏感信息
     * @return 固定的隐藏字符串
     */
    public static String hideSensitiveInfo(String sensitiveInfo) {
        return "[HIDDEN]";
    }

    /**
     * 格式化安全日志消息
     * 统一安全日志的格式
     *
     * @param operation 操作类型
     * @param result 操作结果
     * @param details 详细信息
     * @return 格式化后的日志消息
     */
    public static String formatSecurityLog(String operation, String result, String details) {
        return String.format("[SECURITY] %s - %s | %s", operation, result, details);
    }

    /**
     * 格式化访问日志消息
     * 统一访问日志的格式
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @param ip 客户端IP
     * @param userAgent User Agent
     * @param responseTime 响应时间（毫秒）
     * @return 格式化后的日志消息
     */
    public static String formatAccessLog(String method, String uri, String ip, String userAgent, long responseTime) {
        return String.format("[ACCESS] %s %s | IP: %s | UA: %s | Time: %dms", 
                method, uri, maskIpAddress(ip), maskUserAgent(userAgent), responseTime);
    }

    /**
     * 检查字符串是否包含敏感关键词
     * 用于防止敏感信息意外记录到日志中
     *
     * @param content 要检查的内容
     * @return 是否包含敏感关键词
     */
    public static boolean containsSensitiveKeywords(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        
        String lowerContent = content.toLowerCase();
        String[] sensitiveKeywords = {
                "password", "passwd", "pwd", "secret", "key", "token", 
                "credential", "auth", "密码", "秘钥", "令牌", "凭证"
        };
        
        for (String keyword : sensitiveKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 安全地记录可能包含敏感信息的内容
     * 如果检测到敏感关键词，则进行脱敏处理
     *
     * @param content 原始内容
     * @return 安全的日志内容
     */
    public static String safeLogContent(String content) {
        if (containsSensitiveKeywords(content)) {
            return "[CONTENT_MASKED_DUE_TO_SENSITIVE_KEYWORDS]";
        }
        return content;
    }
}
