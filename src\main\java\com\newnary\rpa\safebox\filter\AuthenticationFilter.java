package com.newnary.rpa.safebox.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.newnary.rpa.safebox.common.BaseResponse;
import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.constant.ResponseConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * API鉴权过滤器
 * 实现基于HmacSHA256的请求签名验证
 * 
 * <AUTHOR>
 * @since Created on 2025-09-18
 */
@Component
@Order(1)
@Slf4j
public class AuthenticationFilter implements Filter {

    @Resource
    private SafeboxProperties safeboxProperties;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Nonce缓存，用于防止重放攻击
     * 缓存时间5分钟
     */
    private final Cache<String, Boolean> nonceCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    /**
     * 时间戳允许的误差范围（毫秒）
     * ±5分钟
     */
    private static final long TIMESTAMP_TOLERANCE = 5 * 60 * 1000L;

    /**
     * HmacSHA256算法名称
     */
    private static final String HMAC_SHA256 = "HmacSHA256";

    /**
     * 请求头名称常量
     */
    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_NONCE = "X-Nonce";
    private static final String HEADER_TIMESTAMP = "X-Timestamp";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 跳过静态资源和Web页面
        String requestURI = httpRequest.getRequestURI();
        if (shouldSkipAuthentication(requestURI)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // 验证鉴权参数
            validateAuthentication(httpRequest);
            
            // 验证通过，继续处理请求
            chain.doFilter(request, response);
            
        } catch (AuthenticationException e) {
            log.warn("鉴权失败: {} - {}", requestURI, e.getMessage());
            handleAuthenticationFailure(httpResponse, e.getMessage(), e.getCode());
        } catch (Exception e) {
            log.error("鉴权过程中发生异常: " + requestURI, e);
            handleAuthenticationFailure(httpResponse, "鉴权过程中发生异常", ResponseConstant.StateCode.SERVER_ERROR);
        }
    }

    /**
     * 判断是否需要跳过鉴权
     * 
     * @param requestURI 请求URI
     * @return 是否跳过鉴权
     */
    private boolean shouldSkipAuthentication(String requestURI) {
        // 跳过静态资源
        if (requestURI.startsWith("/css/") || 
            requestURI.startsWith("/js/") || 
            requestURI.startsWith("/images/") || 
            requestURI.startsWith("/favicon.ico")) {
            return true;
        }
        
        // 跳过Web页面（Thymeleaf模板）
        if (requestURI.equals("/") || requestURI.startsWith("/web/")) {
            return true;
        }

        return false;
    }

    /**
     * 验证鉴权参数
     * 
     * @param request HTTP请求
     * @throws AuthenticationException 鉴权异常
     */
    private void validateAuthentication(HttpServletRequest request) throws AuthenticationException {
        // 获取请求头
        String authorization = request.getHeader(HEADER_AUTHORIZATION);
        String nonce = request.getHeader(HEADER_NONCE);
        String timestampStr = request.getHeader(HEADER_TIMESTAMP);

        // 检查必需的请求头
        if (!StringUtils.hasText(authorization)) {
            throw new AuthenticationException("缺少Authorization请求头", ResponseConstant.StateCode.UNAUTHORIZED);
        }
        if (!StringUtils.hasText(nonce)) {
            throw new AuthenticationException("缺少X-Nonce请求头", ResponseConstant.StateCode.UNAUTHORIZED);
        }
        if (!StringUtils.hasText(timestampStr)) {
            throw new AuthenticationException("缺少X-Timestamp请求头", ResponseConstant.StateCode.UNAUTHORIZED);
        }

        // 验证时间戳格式
        long timestamp;
        try {
            timestamp = Long.parseLong(timestampStr);
        } catch (NumberFormatException e) {
            throw new AuthenticationException("X-Timestamp格式错误", ResponseConstant.StateCode.PARAM_ERROR);
        }

        // 验证时间戳是否在允许范围内
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - timestamp);
        if (timeDiff > TIMESTAMP_TOLERANCE) {
            throw new AuthenticationException("请求时间戳超出允许范围", ResponseConstant.StateCode.UNAUTHORIZED);
        }

        // 检查Nonce是否已被使用（防重放攻击）
        if (nonceCache.getIfPresent(nonce) != null) {
            throw new AuthenticationException("请求已被处理，疑似重放攻击", ResponseConstant.StateCode.UNAUTHORIZED);
        }

        // 验证签名
        String expectedSignature = generateSignature(nonce, timestampStr);
        if (!authorization.equals(expectedSignature)) {
            throw new AuthenticationException("签名验证失败", ResponseConstant.StateCode.UNAUTHORIZED);
        }

        // 将Nonce加入缓存
        nonceCache.put(nonce, true);

        log.info("鉴权验证通过: nonce={}, timestamp={}", nonce, timestampStr);
    }

    /**
     * 生成HmacSHA256签名
     * 
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @return Base64编码的签名
     * @throws AuthenticationException 签名生成异常
     */
    private String generateSignature(String nonce, String timestamp) throws AuthenticationException {
        try {
            String data = nonce + timestamp;
            String accessPassword = safeboxProperties.getAccessPassword();
            
            if (!StringUtils.hasText(accessPassword)) {
                throw new AuthenticationException("系统配置错误：未配置访问密码", ResponseConstant.StateCode.SERVER_ERROR);
            }

            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(accessPassword.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            
            byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signatureBytes);
            
        } catch (Exception e) {
            log.error("生成签名时发生异常", e);
            throw new AuthenticationException("签名生成失败", ResponseConstant.StateCode.SERVER_ERROR);
        }
    }

    /**
     * 处理鉴权失败
     * 
     * @param response HTTP响应
     * @param message 错误消息
     * @param code 错误码
     * @throws IOException IO异常
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String message, String code) 
            throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        BaseResponse<Void> errorResponse = BaseResponse.fail(code, message);
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    /**
     * 鉴权异常类
     */
    private static class AuthenticationException extends Exception {
        private final String code;

        public AuthenticationException(String message, String code) {
            super(message);
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }
}
