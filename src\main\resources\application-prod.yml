# 生产环境配置
logging:
  level:
    root: INFO
    com.newnary.rpa.safebox: INFO
    # 生产环境只记录重要的安全日志
    com.newnary.rpa.safebox.service.SecurityLogService: INFO
    com.newnary.rpa.safebox.service.SecurityManagerService: INFO
    com.newnary.rpa.safebox.service.SecurityApplyService: INFO
    # Spring框架日志保持WARN级别
    org.springframework.web: WARN
    org.springframework.security: WARN
    # 第三方库保持ERROR级别
    org.linguafranca.pwdb: ERROR
    com.larksuite.oapi: ERROR
  file:
    name: ./logs/safebox.log

# 生产环境特定配置
server:
  port: 8080

spring:
  thymeleaf:
    cache: true  # 生产环境启用模板缓存
  web:
    resources:
      cache:
        period: 86400  # 生产环境启用长时间静态资源缓存（24小时）

safebox:
  access-password: uz4Xe