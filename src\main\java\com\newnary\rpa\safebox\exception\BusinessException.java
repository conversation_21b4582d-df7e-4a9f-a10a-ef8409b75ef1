package com.newnary.rpa.safebox.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessException extends RuntimeException {

    /**
     * 构造函数
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * @param message 错误消息
     * @param data 业务数据
     */
    public BusinessException(String message, Object data) {
        super(message);
    }

    /**
     * 构造函数
     * @param message 错误消息
     * @param cause 异常原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     * @param code 错误码
     * @param message 错误消息
     * @param cause 异常原因
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
    }
}