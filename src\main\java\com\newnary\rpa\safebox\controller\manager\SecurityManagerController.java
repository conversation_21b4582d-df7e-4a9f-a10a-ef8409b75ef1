package com.newnary.rpa.safebox.controller.manager;

import com.newnary.rpa.safebox.common.BaseResponse;
import com.newnary.rpa.safebox.common.SecurityLite;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseMetadata;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseUploadResponse;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 密码管理控制器
 * 提供密码条目的增删改查和主密码管理API
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/security-manager")
@Slf4j
public class SecurityManagerController {

    @Resource
    private SecurityManagerService securityManagerService;

    /**
     * 上传KeePass数据库
     *
     * @param file             数据库文件
     * @param originalPassword 原始主密码
     * @param newPassword      新主密码（可选）
     * @return 上传结果
     */
    @PostMapping("/upload")
    public BaseResponse<DatabaseUploadResponse> uploadDatabase(
            @RequestParam("file") MultipartFile file,
            @RequestParam("originalPassword") String originalPassword,
            @RequestParam(value = "newPassword", required = false) String newPassword) {
        try {
            DatabaseUploadResponse response = securityManagerService.uploadDatabase(file, originalPassword, newPassword);
            return BaseResponse.success("数据库上传成功", response);
        } catch (Exception e) {
            log.error("上传数据库失败", e);
            return BaseResponse.fail("上传数据库失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库元数据
     *
     * @return 数据库元数据
     */
    @GetMapping("/metadata")
    public BaseResponse<DatabaseMetadata> getDatabaseMetadata() {
        try {
            DatabaseMetadata metadata = securityManagerService.getDatabaseMetadata();
            return BaseResponse.success("获取数据库元数据成功", metadata);
        } catch (Exception e) {
            log.error("获取数据库元数据失败", e);
            return BaseResponse.fail("获取数据库元数据失败: " + e.getMessage());
        }
    }

    /**
     * 删除数据库
     *
     * @param masterPassword 主密码
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public BaseResponse<Boolean> deleteUserDatabase(@RequestParam("masterPassword") String masterPassword) {
        try {
            Boolean result = securityManagerService.deleteUserDatabase(masterPassword);
            return BaseResponse.success("数据库删除成功", result);
        } catch (Exception e) {
            log.error("删除数据库失败", e);
            return BaseResponse.fail("删除数据库失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有条目
     *
     * @param masterPassword 主密码
     * @return 条目List
     **/
    @PostMapping("/getAllEntries")
    public BaseResponse<List<SecurityLite>> getAllEntries(@RequestParam("masterPassword") String masterPassword) {
        try {
            List<SecurityLite> entries = securityManagerService.getAllEntries(masterPassword);
            return BaseResponse.success("获取所有条目成功", entries);
        } catch (Exception e) {
            log.error("获取所有条目失败", e);
            return BaseResponse.fail("获取所有条目失败: " + e.getMessage());
        }
    }

}
