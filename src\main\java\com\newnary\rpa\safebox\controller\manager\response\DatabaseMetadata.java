package com.newnary.rpa.safebox.controller.manager.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据库元数据DTO
 * 存储KeePass数据库的元信息
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 */
@Data
public class DatabaseMetadata {

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 文件大小（字节）
     */
    private long fileSize;

    /**
     * 密码条目数量
     */
    private int entryCount;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;

    /**
     * 数据库版本
     */
    private String version;

    /**
     * 加密算法
     */
    private String encryptionAlgorithm;

    /**
     * 密钥派生函数
     */
    private String keyDerivation;

    /**
     * 迭代次数
     */
    private long iterations;

    /**
     * 内存使用量（KB）
     */
    private long memory;

    /**
     * 并行度
     */
    private int parallelism;

    /**
     * 压缩算法
     */
    private String compression;

    /**
     * 数据库描述
     */
    private String description;

    /**
     * 文件哈希值（用于完整性验证）
     */
    private String fileHash;
}
