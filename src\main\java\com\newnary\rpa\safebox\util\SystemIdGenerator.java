package com.newnary.rpa.safebox.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Enumeration;
import java.util.StringJoiner;

/**
 * 系统ID生成器
 * 基于系统硬件信息生成唯一标识符
 *
 * <AUTHOR>
 * @since Created on 2025-09-19
 **/
public class SystemIdGenerator {

    public static String generateSystemId() throws Exception {
        StringJoiner systemInfo = new StringJoiner("-");

        // 1. 获取MAC地址（首选非虚拟网卡）
        String macAddress = getMacAddress();
        systemInfo.add(macAddress);

        // 2. 获取CPU序列号（Windows/Linux）
        String cpuSerial = getCpuSerial();
        systemInfo.add(cpuSerial);

        // 3. 获取主板序列号（Windows）
        String baseboardSerial = getBaseboardSerial();
        systemInfo.add(baseboardSerial);

        // 4. 操作系统名称+架构
        String osInfo = System.getProperty("os.name") + "_" + System.getProperty("os.arch");
        systemInfo.add(osInfo);

        // 5. 当前用户名（新增点）
        String userName = System.getProperty("user.name");
        systemInfo.add("User:" + userName);

        // 6. 计算SHA-256哈希值（固定长度）
        return sha256Hash(systemInfo.toString());
    }

    // 获取MAC地址（跳过虚拟网卡）
    private static String getMacAddress() throws Exception {
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface networkInterface = interfaces.nextElement();
            if (!networkInterface.isLoopback() && !networkInterface.isVirtual() && networkInterface.getHardwareAddress() != null) {
                byte[] mac = networkInterface.getHardwareAddress();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    sb.append(String.format("%02X", mac[i]));
                    if (i < mac.length - 1) sb.append(":");
                }
                return sb.toString();
            }
        }
        return "NO_MAC";
    }

    // 获取CPU序列号（Windows/Linux命令）
    private static String getCpuSerial() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            if (os.contains("win")) {
                process = Runtime.getRuntime().exec(new String[]{"wmic", "cpu", "get", "ProcessorId"});
            } else if (os.contains("linux")) {
                process = Runtime.getRuntime().exec(new String[]{"cat", "/proc/cpuinfo"});
            } else {
                return "UNKNOWN_CPU";
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                StringBuilder output = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    if (line.toLowerCase().contains("processor id") || line.toLowerCase().contains("serial")) {
                        output.append(line.trim()).append("\n");
                    }
                }
                return output.length() > 0 ? output.toString().trim() : "NO_CPU_SERIAL";
            }
        } catch (Exception e) {
            return "CPU_ERROR";
        }
    }

    // 获取主板序列号（Windows）
    private static String getBaseboardSerial() {
        try {
            Process process = Runtime.getRuntime().exec(new String[]{"wmic", "baseboard", "get", "serialnumber"});
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                StringBuilder output = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().equalsIgnoreCase("serialnumber")) {
                        output.append(line.trim());
                    }
                }
                return output.length() > 0 ? output.toString() : "NO_BASEBOARD_SERIAL";
            }
        } catch (Exception e) {
            return "BOARD_ERROR";
        }
    }

    // SHA-256哈希计算
    private static String sha256Hash(String input) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }

}
