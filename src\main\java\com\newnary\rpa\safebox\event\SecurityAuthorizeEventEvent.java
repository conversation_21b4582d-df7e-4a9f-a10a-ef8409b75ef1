package com.newnary.rpa.safebox.event;

import com.newnary.rpa.safebox.common.SecurityApplyCard;
import org.springframework.context.ApplicationEvent;

/**
 * 密码申请审核验证
 *
 * <AUTHOR>
 * @since Created on 2025-09-15
 **/
public class SecurityAuthorizeEventEvent extends ApplicationEvent {

    public SecurityAuthorizeEventEvent(SecurityApplyCard source) {
        super(source);
    }

    @Override
    public SecurityApplyCard getSource() {
        return (SecurityApplyCard) super.getSource();
    }

}
