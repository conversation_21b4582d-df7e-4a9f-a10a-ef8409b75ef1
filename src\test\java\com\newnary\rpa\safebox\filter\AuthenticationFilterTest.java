package com.newnary.rpa.safebox.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newnary.rpa.safebox.common.BaseResponse;
import com.newnary.rpa.safebox.config.SafeboxProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 鉴权过滤器测试类
 * 
 * <AUTHOR>
 * @since Created on 2025-09-18
 */
@ExtendWith(MockitoExtension.class)
class AuthenticationFilterTest {

    @Mock
    private SafeboxProperties safeboxProperties;

    @InjectMocks
    private AuthenticationFilter authenticationFilter;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private MockFilterChain filterChain;
    private ObjectMapper objectMapper;

    private static final String TEST_ACCESS_PASSWORD = "testPassword123";
    private static final String HMAC_SHA256 = "HmacSHA256";

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();
        objectMapper = new ObjectMapper();

        // 模拟配置
        when(safeboxProperties.getAccessPassword()).thenReturn(TEST_ACCESS_PASSWORD);
    }

    @Test
    void testValidAuthentication() throws ServletException, IOException {
        // 准备测试数据
        String nonce = "test-nonce-12345";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = generateTestSignature(nonce, timestamp);

        // 设置请求
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", signature);
        request.addHeader("X-Nonce", nonce);
        request.addHeader("X-Timestamp", timestamp);

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(200, response.getStatus());
        assertTrue(filterChain.getRequest() != null);
    }

    @Test
    void testMissingAuthorizationHeader() throws ServletException, IOException {
        // 设置请求（缺少Authorization头）
        request.setRequestURI("/api/test");
        request.addHeader("X-Nonce", "test-nonce");
        request.addHeader("X-Timestamp", String.valueOf(System.currentTimeMillis()));

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("缺少Authorization请求头"));
    }

    @Test
    void testMissingNonceHeader() throws ServletException, IOException {
        // 设置请求（缺少X-Nonce头）
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", "test-auth");
        request.addHeader("X-Timestamp", String.valueOf(System.currentTimeMillis()));

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("缺少X-Nonce请求头"));
    }

    @Test
    void testMissingTimestampHeader() throws ServletException, IOException {
        // 设置请求（缺少X-Timestamp头）
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", "test-auth");
        request.addHeader("X-Nonce", "test-nonce");

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("缺少X-Timestamp请求头"));
    }

    @Test
    void testInvalidTimestampFormat() throws ServletException, IOException {
        // 设置请求（时间戳格式错误）
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", "test-auth");
        request.addHeader("X-Nonce", "test-nonce");
        request.addHeader("X-Timestamp", "invalid-timestamp");

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("X-Timestamp格式错误"));
    }

    @Test
    void testTimestampOutOfRange() throws ServletException, IOException {
        // 设置请求（时间戳超出范围）
        String oldTimestamp = String.valueOf(System.currentTimeMillis() - 10 * 60 * 1000L); // 10分钟前
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", "test-auth");
        request.addHeader("X-Nonce", "test-nonce");
        request.addHeader("X-Timestamp", oldTimestamp);

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("请求时间戳超出允许范围"));
    }

    @Test
    void testInvalidSignature() throws ServletException, IOException {
        // 设置请求（签名错误）
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", "invalid-signature");
        request.addHeader("X-Nonce", "test-nonce");
        request.addHeader("X-Timestamp", String.valueOf(System.currentTimeMillis()));

        // 执行过滤器
        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("签名验证失败"));
    }

    @Test
    void testReplayAttack() throws ServletException, IOException {
        // 准备测试数据
        String nonce = "replay-test-nonce";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = generateTestSignature(nonce, timestamp);

        // 第一次请求
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", signature);
        request.addHeader("X-Nonce", nonce);
        request.addHeader("X-Timestamp", timestamp);

        authenticationFilter.doFilter(request, response, filterChain);
        assertEquals(200, response.getStatus());

        // 重置响应
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();

        // 第二次请求（相同的nonce）
        request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.addHeader("Authorization", signature);
        request.addHeader("X-Nonce", nonce);
        request.addHeader("X-Timestamp", timestamp);

        authenticationFilter.doFilter(request, response, filterChain);

        // 验证结果
        assertEquals(401, response.getStatus());
        assertTrue(response.getContentAsString().contains("请求已被处理，疑似重放攻击"));
    }

    @Test
    void testSkipStaticResources() throws ServletException, IOException {
        // 测试静态资源跳过鉴权
        request.setRequestURI("/css/style.css");

        authenticationFilter.doFilter(request, response, filterChain);

        assertEquals(200, response.getStatus());
        assertTrue(filterChain.getRequest() != null);
    }

    @Test
    void testSkipWebPages() throws ServletException, IOException {
        // 测试Web页面跳过鉴权
        request.setRequestURI("/");

        authenticationFilter.doFilter(request, response, filterChain);

        assertEquals(200, response.getStatus());
        assertTrue(filterChain.getRequest() != null);
    }

    /**
     * 生成测试用的HmacSHA256签名
     * 
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @return Base64编码的签名
     */
    private String generateTestSignature(String nonce, String timestamp) {
        try {
            String data = nonce + timestamp;
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(TEST_ACCESS_PASSWORD.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signatureBytes);
        } catch (Exception e) {
            throw new RuntimeException("生成测试签名失败", e);
        }
    }
}
