# 密码管理流程设计

## 概述

本文档详细描述了基于KeePass的密码管理系统的完整流程设计，包括密码库管理、密码库重新保存和批量密码获取三个核心功能。

## 1. 密码库管理流程

### 1.1 整体架构

```
用户本地KeePass → 文件上传 → 系统验证 → 密码库托管 → 只读访问
```

### 1.2 详细流程

#### 1.2.1 用户侧操作

1. **本地密码库创建/管理**
   - 用户使用KeePass客户端在本地创建或管理密码库
   - 设置主密码和安全参数
   - 添加、修改、删除密码条目
   - 导出为.kdbx文件

#### 1.2.2 文件上传流程

1. **上传接口**

   - 提供文件上传API接口
   - 支持multipart/form-data格式
   - 文件大小限制：最大10MB
   - 文件类型验证：仅允许.kdbx文件
2. **用户身份验证**

   - 用户ID/应用ID验证
   - 可选：额外的身份验证机制
3. **临时存储**

   - 上传的文件先存储到临时目录
   - 生成唯一的临时文件标识

#### 1.2.3 密码库验证流程

1. **文件格式验证**

   - 验证文件是否为有效的KeePass数据库格式
   - 检查文件头和基本结构
2. **密码库完整性检查**

   - 尝试使用用户提供的主密码打开数据库
   - 验证数据库内容的完整性
   - 检查是否存在损坏的条目
3. **安全性检查**

   - 检查密码库的加密强度
   - 验证密码复杂度要求
   - 可选：病毒扫描

#### 1.2.4 密码库托管

1. **存储结构**

   ```
   {kdbxPath}/
   ├── {userId}/
   │   ├── original/
   │   │   └── database.kdbx          # 原始上传文件
   │   ├── managed/
   │   │   └── database.kdbx          # 系统管理的文件
   │   └── metadata.json              # 元数据信息
   ```
2. **元数据管理**

   ```json
   {
     "uploadTime": "2025-09-12T10:00:00Z",
     "originalFileName": "my_passwords.kdbx",
     "fileSize": 1024,
     "entryCount": 50,
     "lastModified": "2025-09-12T10:00:00Z",
     "version": "4.0",
     "encryptionAlgorithm": "AES-256",
     "keyDerivation": "Argon2d"
   }
   ```
3. **访问控制**

   - 系统对密码库只有读取权限
   - 不允许系统修改原始密码库内容
   - 用户可以重新上传覆盖现有密码库

## 2. 密码库重新保存机制

### 2.1 设计目标

- 使用系统预设的安全参数统一密码库格式
- 保持原有密码库内容完全不变
- 提高系统处理效率和安全性

### 2.2 预设参数配置

```yaml
keepass:
  preset-params:
    version: 4                    # KeePass版本
    encryption: AES-256           # 加密算法
    key-derivation: Argon2d       # 密钥派生函数
    iterations: 100000            # 迭代次数
    memory: 65536                 # 内存使用(KB)
    parallelism: 2                # 并行度
    compression: GZip             # 压缩算法
```

### 2.3 重新保存流程

1. **加载原始密码库**

   - 使用用户提供的主密码加载原始数据库
   - 读取所有密码条目和组结构
2. **创建新数据库**

   - 使用预设参数创建新的空白数据库
   - 设置统一的安全参数
3. **数据迁移**

   - 复制所有密码条目到新数据库
   - 保持原有的组织结构
   - 保留所有自定义字段和属性
4. **保存管理版本**

   - 使用相同的主密码保存到managed目录
   - 验证数据完整性
   - 更新元数据信息

## 3. 批量密码获取功能

### 3.1 功能特性

- 支持用户提供主密码进行身份验证
- 支持按title列表批量获取密码条目
- 返回匹配的完整密码信息
- 支持模糊匹配和精确匹配

### 3.2 API设计

```http
POST /api/passwords/batch-retrieve
Content-Type: application/json

{
  "userId": "user123",
  "masterPassword": "user_master_password",
  "titles": ["Gmail", "GitHub", "AWS Console"],
  "matchType": "EXACT",  // EXACT, FUZZY, CONTAINS
  "includeMetadata": true
}
```

### 3.3 响应格式

```json
{
  "state": "SUCCESS",
  "message": "成功获取密码条目",
  "data": {
    "totalRequested": 3,
    "totalFound": 2,
    "entries": [
      {
        "title": "Gmail",
        "username": "<EMAIL>",
        "url": "https://gmail.com",
        "notes": "Personal email account",
        "lastModified": "2025-09-10T15:30:00Z"
      },
      {
        "title": "GitHub",
        "username": "developer123",
        "url": "https://github.com",
        "notes": "Development account",
        "lastModified": "2025-09-11T09:15:00Z"
      }
    ],
    "notFound": ["AWS Console"]
  }
}
```

### 3.4 安全考虑

1. **密码验证**

   - 每次请求都需要验证主密码
   - 限制密码错误尝试次数
   - 实现账户锁定机制
2. **访问控制**

   - 用户只能访问自己的密码库
   - 记录所有访问日志
   - 支持访问频率限制
3. **数据传输安全**

   - 使用HTTPS加密传输
   - 可选：额外的端到端加密
   - 敏感数据不记录到日志
