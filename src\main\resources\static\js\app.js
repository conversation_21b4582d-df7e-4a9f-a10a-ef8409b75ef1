// 密码管理系统JavaScript

// 密码生成器
function generatePassword() {
    const length = 6; // 固定6位
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const allChars = uppercase + lowercase + numbers;

    let password = '';

    // 确保至少包含一个大写字母、小写字母和数字
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];

    // 填充剩余位数
    for (let i = 3; i < length; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打乱密码字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

// 生成新密码并填充到输入框
function generateNewPassword() {
    const newPassword = generatePassword();
    const newPasswordInput = document.getElementById('newPassword');
    newPasswordInput.value = newPassword;
}

// 切换密码显示/隐藏
function togglePasswordVisibility(inputId, eyeId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(eyeId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.textContent = '🙈'; // 隐藏眼睛
    } else {
        passwordInput.type = 'password';
        eyeIcon.textContent = '👁️'; // 显示眼睛
    }
}

// 工具函数
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;

    const content = document.querySelector('.content');
    content.insertBefore(alertDiv, content.firstChild);

    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 密码管理功能
const AUTH_PASSWORD_KEY = 'api_auth_password';

// 保存密码到sessionStorage
function saveAuthPassword(password) {
    sessionStorage.setItem(AUTH_PASSWORD_KEY, password);
}

// 从sessionStorage获取密码
function getAuthPassword() {
    return sessionStorage.getItem(AUTH_PASSWORD_KEY);
}

// 清除保存的密码
function clearAuthPassword() {
    sessionStorage.removeItem(AUTH_PASSWORD_KEY);
    showAuthModal();
}

// 检查是否有保存的密码
function hasAuthPassword() {
    return !!getAuthPassword();
}

// 显示鉴权模态框
function showAuthModal() {
    document.getElementById('authModal').style.display = 'block';
    document.getElementById('authPassword').focus();
}

// 隐藏鉴权模态框
function hideAuthModal() {
    document.getElementById('authModal').style.display = 'none';
    document.getElementById('authError').style.display = 'none';
    document.getElementById('authPassword').value = '';
}

// 显示鉴权错误
function showAuthError(message) {
    const errorDiv = document.getElementById('authError');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
}

// 提交鉴权密码
async function submitAuthPassword() {
    const password = document.getElementById('authPassword').value.trim();
    if (!password) {
        showAuthError('请输入API访问密码');
        return;
    }

    // 测试密码是否正确（通过尝试调用一个API）
    try {
        const testResult = await testAuthPassword(password);
        if (testResult) {
            saveAuthPassword(password);
            hideAuthModal();
            showAlert('鉴权验证成功', 'success');
        } else {
            showAuthError('密码错误，请重新输入');
        }
    } catch (error) {
        showAuthError('验证失败：' + error.message);
    }
}

// 测试密码是否正确
async function testAuthPassword(password) {
    try {
        const authHeaders = generateAuthHeaders(password);
        const response = await fetch('/security-manager/metadata', {
            method: 'GET',
            headers: {
                'Authorization': authHeaders.authorization,
                'X-Nonce': authHeaders.nonce,
                'X-Timestamp': authHeaders.timestamp
            }
        });

        return response.ok;
    } catch (error) {
        console.error('测试密码失败:', error);
        return false;
    }
}

// 生成鉴权头（客户端计算）
function generateAuthHeaders(password) {
    // 生成随机nonce（24字节，Base64编码）
    const nonceArray = new Uint8Array(24);
    crypto.getRandomValues(nonceArray);
    const nonce = btoa(String.fromCharCode.apply(null, nonceArray))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    // 生成时间戳
    const timestamp = Date.now().toString();

    // 生成签名数据（nonce + timestamp）
    const data = nonce + timestamp;

    // 使用HmacSHA256计算签名
    const signature = CryptoJS.HmacSHA256(data, password).toString(CryptoJS.enc.Base64);

    return {
        authorization: signature,
        nonce: nonce,
        timestamp: timestamp
    };
}

// 创建带鉴权的fetch请求
async function authenticatedFetch(url, options = {}) {
    const password = getAuthPassword();
    if (!password) {
        showAuthModal();
        throw new Error('未设置API访问密码');
    }

    try {
        const authHeaders = generateAuthHeaders(password);

        const headers = {
            'Authorization': authHeaders.authorization,
            'X-Nonce': authHeaders.nonce,
            'X-Timestamp': authHeaders.timestamp,
            ...options.headers
        };

        // 如果不是FormData，则设置Content-Type
        if (!(options.body instanceof FormData)) {
            headers['Content-Type'] = 'application/json';
        }

        const response = await fetch(url, {
            ...options,
            headers
        });

        // 如果返回401，可能是密码错误，清除保存的密码
        if (response.status === 401) {
            clearAuthPassword();
            throw new Error('鉴权失败，请重新输入密码');
        }

        return response;
    } catch (error) {
        console.error('鉴权请求失败:', error);
        throw error;
    }
}

function showLoading(element) {
    element.innerHTML = '<div class="loading">处理中...</div>';
}

function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}

// 上传数据库
function uploadDatabase() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData();
    const resultDiv = document.getElementById('uploadResult');

    // 获取表单数据
    const file = document.getElementById('file').files[0];
    const originalPassword = document.getElementById('originalPassword').value;
    const newPassword = document.getElementById('newPassword').value;

    if (!file) {
        showAlert('请选择要上传的文件', 'danger');
        return;
    }

    if (!originalPassword) {
        showAlert('请输入原始主密码', 'danger');
        return;
    }

    // 构建FormData
    formData.append('file', file);
    formData.append('originalPassword', originalPassword);
    if (newPassword && newPassword.trim() !== '') {
        formData.append('newPassword', newPassword);
    }

    showLoading(resultDiv);

    authenticatedFetch('/security-manager/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.state === 'SUCCESS') {
            const passwordChanged = newPassword && newPassword.trim() !== '' && newPassword !== originalPassword;
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h4>上传成功！</h4>
                    <p>文件名: ${data.data.originalFileName}</p>
                    <p>文件大小: ${data.data.fileSize} 字节</p>
                    <p>条目数量: ${data.data.entryCount}</p>
                    <p>上传时间: ${data.data.uploadTime}</p>
                    ${passwordChanged ? '<p><strong>主密码已更新</strong></p>' : ''}
                </div>
            `;
            form.reset();
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h4>上传失败</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h4>上传失败</h4>
                <p>网络错误: ${error.message}</p>
            </div>
        `;
    });
}

// 获取数据库元数据
async function loadMetadata() {
    const resultDiv = document.getElementById('metadataResult');
    showLoading(resultDiv);

    try {
        const response = await authenticatedFetch('/security-manager/metadata');
        const data = await response.json();

        if (data.state === 'SUCCESS') {
            const metadata = data.data;
            resultDiv.innerHTML = `
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <h4>文件大小</h4>
                        <p>${metadata.fileSize} 字节</p>
                    </div>
                    <div class="metadata-item">
                        <h4>条目数量</h4>
                        <p>${metadata.entryCount}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>上传时间</h4>
                        <p>${metadata.uploadTime}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>最后修改</h4>
                        <p>${metadata.lastModified}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>版本</h4>
                        <p>${metadata.version}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>加密算法</h4>
                        <p>${metadata.encryptionAlgorithm}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>密钥派生</h4>
                        <p>${metadata.keyDerivation}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>迭代次数</h4>
                        <p>${metadata.iterations}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>内存使用</h4>
                        <p>${metadata.memory} KB</p>
                    </div>
                    <div class="metadata-item">
                        <h4>并行度</h4>
                        <p>${metadata.parallelism}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>压缩算法</h4>
                        <p>${metadata.compression}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>文件哈希</h4>
                        <p style="font-size: 0.9em; word-break: break-all;">${metadata.fileHash}</p>
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h4>获取元数据失败</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h4>获取元数据失败</h4>
                <p>网络错误: ${error.message}</p>
            </div>
        `;
    }
}

// 获取所有条目
async function loadEntries() {
    const password = document.getElementById('entriesPassword').value;
    if (!password) {
        showAlert('请输入主密码', 'danger');
        return;
    }

    const resultDiv = document.getElementById('entriesResult');
    showLoading(resultDiv);

    const formData = new FormData();
    formData.append('masterPassword', password);

    try {
        const response = await authenticatedFetch('/security-manager/getAllEntries', {
            method: 'POST',
            body: formData,
            headers: {} // FormData会自动设置Content-Type
        });
        const data = await response.json();

        if (data.state === 'SUCCESS') {
            const entries = data.data;
            if (entries.length === 0) {
                resultDiv.innerHTML = `
                    <div class="alert alert-info">
                        <p>数据库中没有找到任何条目</p>
                    </div>
                `;
            } else {
                let tableHTML = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>用户名</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                entries.forEach(entry => {
                    tableHTML += `
                        <tr>
                            <td>${entry.title}</td>
                            <td>${entry.username}</td>
                        </tr>
                    `;
                });
                
                tableHTML += `
                        </tbody>
                    </table>
                `;
                
                resultDiv.innerHTML = tableHTML;
            }
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h4>获取条目失败</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h4>获取条目失败</h4>
                <p>网络错误: ${error.message}</p>
            </div>
        `;
    }
}

// 删除数据库
async function deleteDatabase() {
    const password = document.getElementById('deletePassword').value;
    if (!password) {
        showAlert('请输入主密码', 'danger');
        return;
    }

    if (!confirm('确定要删除数据库吗？此操作不可恢复！')) {
        return;
    }

    const resultDiv = document.getElementById('deleteResult');
    showLoading(resultDiv);

    const formData = new FormData();
    formData.append('masterPassword', password);

    try {
        const response = await authenticatedFetch('/security-manager/delete', {
            method: 'DELETE',
            body: formData,
            headers: {} // FormData会自动设置Content-Type
        });
        const data = await response.json();

        if (data.state === 'SUCCESS') {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h4>删除成功</h4>
                    <p>数据库已成功删除</p>
                </div>
            `;
            document.getElementById('deletePassword').value = '';
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h4>删除失败</h4>
                    <p>${data.message}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h4>删除失败</h4>
                <p>网络错误: ${error.message}</p>
            </div>
        `;
    }
}

// 页面导航
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.add('hidden'));
    
    // 显示指定页面
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.remove('hidden');
    }
    
    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => link.classList.remove('active'));
    
    const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // 特殊处理：如果是元数据页面，自动加载数据
    if (pageId === 'metadataPage') {
        loadMetadata();
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示首页
    showPage('homePage');

    // 检查是否有保存的鉴权密码
    if (!hasAuthPassword()) {
        showAuthModal();
    }
});
