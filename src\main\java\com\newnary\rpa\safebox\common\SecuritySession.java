package com.newnary.rpa.safebox.common;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since Created on 2025-09-12
 **/
@Data
public class SecuritySession {

    /**
     * session
     **/
    private String sessionId;

    /**
     * 申请的密码项(标题)
     **/
    private List<String> applyList;

    /**
     * 审核状态
     **/
    private State state;

    /**
     * 结果项
     **/
    private List<SecurityEntryWrap> securityList;

    public static SecuritySession createWith(List<String> applyList) {
        if (CollectionUtils.isEmpty(applyList)) {
            throw new IllegalArgumentException("applyList must not be empty");
        }
        if (applyList.size() > 100) {
            throw new IllegalArgumentException("applyList must not exceed 100 elements");
        }

        SecuritySession session = new SecuritySession();
        session.setSessionId(UUID.randomUUID().toString());
        session.setState(State.WAIT);
        session.setApplyList(applyList);
        session.setSecurityList(applyList.stream().map(SecurityEntryWrap::new).collect(Collectors.toList()));
        return session;
    }

    public void approved(List<SecurityEntry> securityList) {
        if (state == State.WAIT) {
            setState(State.APPROVED);

            Map<String, SecurityEntryWrap> collect = getSecurityList().stream().collect(Collectors.toMap(SecurityEntryWrap::getApplyKey, Function.identity()));
            securityList.forEach(securityEntry -> {
                SecurityEntryWrap entryWrap = collect.get(securityEntry.getTitle());
                if (entryWrap != null) {
                    entryWrap.setState("success");
                    entryWrap.setEntry(securityEntry);
                }
            });

            setSecurityList(new ArrayList<>(collect.values()));
        } else {
            throw new RuntimeException("State is not WAIT");
        }
    }

    public void rejected() {
        if (state == State.WAIT) {
            setState(State.REJECTED);
        } else {
            throw new RuntimeException("State is not WAIT");
        }
    }

    public enum State {
        WAIT,
        APPROVED,
        REJECTED,
        ;
    }

}
