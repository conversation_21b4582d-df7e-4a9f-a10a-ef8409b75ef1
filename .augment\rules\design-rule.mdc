---
alwaysApply: true
---
# 通用原则
- 交互语言: AI必须始终使用中文与用户进行交互。

# 编码规范说明
- **注释:** 所有公开的类、方法、变量以及复杂的业务逻辑代码块，都必须提供清晰、完整的 Javadoc 注释。
- **依赖注入:** 除非有特殊理由，应优先使用 `@Resource`, 在无法满足的情况下才考虑使用 Spring 的 `@Autowired` 或 JSR-330 的 `@Inject`。
- **POJO:** 实体类（Entity）、数据传输对象（DTO）等 POJO 类，请始终使用 Lombok 来简化代码 (`@Data`, `@Getter`, `@Setter`, `@ToString` 等), 但慎用Build或构造函数注解 (`@NoArgsConstructor`, `@AllArgsConstructor` 等)
- **模型分层:** 实体类（Entity、 PO）只在和持久层交互时使用, Service / Controller 等应封装 VO / DTO 对象
- **面向领域编程:** 遵守 DDD 思想, 谨慎、完善的分析模型

# 单元测试
- 请始终使用 JUnit 5 进行单元测试, 并保持 100% 的测试覆盖率。
- **维护:** 测试用例必须与业务逻辑的变更保持同步更新。

# 工作流程
- 遇到 API 模糊、版本差异大或用户请求查阅官方用法时使用 @context7 从外部文档、API参考和代码示例中获取权威信息，澄清用户请求。
- 如果提供了 README.md，请先结合 PDM (产品经理) 的思考导向，明确核心问题与用户价值。
- 产出: 对用户需求的清晰定义、关键验收标准（AC）以及引用的上下文来源。
- 遇到不清楚的内容应立即向用户提问。
- 注意在任务完成前根据 @mcp-feedback-enhanced 工具的要求进行工具调用

# 技术栈说明
- 本项目使用 Java 语言开发, 基于 Spring Boot 框架, 使用 Maven 进行项目管理, 请始终使用 Maven 进行项目构建和管理。
- 本工程核心依赖 KeePassJava2 库, 请始终使用该库进行 KeePass 数据库的读写操作。(参考: https://github.com/jorabin/KeePassJava2)
- 基于 SpringBoot 2.7.x
- 本工程的核心需求是管理账号密码, 请遵循密码学规范