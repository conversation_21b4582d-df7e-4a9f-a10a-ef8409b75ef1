package com.newnary.rpa.safebox.example;

import com.newnary.rpa.safebox.keepass.KeePassManager;
import org.linguafranca.pwdb.kdbx.KdbxCreds;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonEntry;
import org.linguafranca.pwdb.kdbx.jackson.JacksonGroup;

/**
 * 创建测试数据库
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 */
public class CreateTestDatabase {

    public static void main(String[] args) {
        try {
            KeePassManager keePassManager = new KeePassManager();
            
            // 创建空白数据库
            JacksonDatabase database = keePassManager.createEmptyDatabase();
            
            // 获取根组
            JacksonGroup rootGroup = database.getRootGroup();
            
            // 创建测试条目A
            JacksonEntry entryA = JacksonEntry.createEntry(database);
            entryA.setTitle("Gmail Account");
            entryA.setUsername("<EMAIL>");
            entryA.setPassword("gmail_password_123");
            entryA.setUrl("https://gmail.com");
            entryA.setNotes("Personal Gmail account");
            rootGroup.addEntry(entryA);
            
            // 创建测试条目B
            JacksonEntry entryB = JacksonEntry.createEntry(database);
            entryB.setTitle("GitHub Account");
            entryB.setUsername("developer123");
            entryB.setPassword("github_secure_pass");
            entryB.setUrl("https://github.com");
            entryB.setNotes("Development GitHub account");
            rootGroup.addEntry(entryB);
            
            // 创建测试条目C
            JacksonEntry entryC = JacksonEntry.createEntry(database);
            entryC.setTitle("AWS Console");
            entryC.setUsername("aws_admin");
            entryC.setPassword("aws_complex_password_456");
            entryC.setUrl("https://console.aws.amazon.com");
            entryC.setNotes("AWS management console");
            rootGroup.addEntry(entryC);
            
            // 保存数据库
            String filePath = "src/main/resources/test_database.kdbx";
            keePassManager.save(database, filePath, new KdbxCreds("123123".getBytes()));
            
            System.out.println("测试数据库创建成功: " + filePath);
            System.out.println("密码: 123123");
            System.out.println("包含条目:");
            System.out.println("1. Gmail Account (<EMAIL>)");
            System.out.println("2. GitHub Account (developer123)");
            System.out.println("3. AWS Console (aws_admin)");
            
            // 验证数据库可以正常加载
            JacksonDatabase loadedDb = keePassManager.load(filePath, new KdbxCreds("123123".getBytes()));
            System.out.println("验证: 数据库加载成功，包含 " + keePassManager.getAllEntries(loadedDb).size() + " 个条目");
            
        } catch (Exception e) {
            System.err.println("创建测试数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
