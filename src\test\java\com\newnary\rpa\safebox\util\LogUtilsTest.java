package com.newnary.rpa.safebox.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 日志工具类测试
 *
 * <AUTHOR>
 * @since Created on 2025-09-19
 */
@Slf4j
@SpringBootTest
class LogUtilsTest {

    @Test
    void testMaskUsername() {
        // 测试用户名脱敏
        assertEquals("***", LogUtils.maskUsername(""));
        assertEquals("***", LogUtils.maskUsername(null));
        assertEquals("***", LogUtils.maskUsername("ab"));
        assertEquals("a***c", LogUtils.maskUsername("abc"));
        assertEquals("a***d", LogUtils.maskUsername("abcd"));
        assertEquals("ab***d", LogUtils.maskUsername("abcdef"));
        
        log.info("用户名脱敏测试完成");
    }

    @Test
    void testMaskIpAddress() {
        // 测试IP地址脱敏
        assertEquals("***.***.***.***", LogUtils.maskIpAddress(""));
        assertEquals("***.***.***.***", LogUtils.maskIpAddress(null));
        assertEquals("***.***.***.***", LogUtils.maskIpAddress("invalid"));
        assertEquals("192.168.***.***.***", LogUtils.maskIpAddress("***********"));
        
        log.info("IP地址脱敏测试完成");
    }

    @Test
    void testMaskSessionId() {
        // 测试会话ID脱敏
        assertEquals("****-****-****", LogUtils.maskSessionId(""));
        assertEquals("****-****-****", LogUtils.maskSessionId(null));
        assertEquals("****-****-****", LogUtils.maskSessionId("short"));
        assertEquals("abcd-****-5678", LogUtils.maskSessionId("abcd12345678"));
        
        log.info("会话ID脱敏测试完成");
    }

    @Test
    void testContainsSensitiveKeywords() {
        // 测试敏感关键词检测
        assertFalse(LogUtils.containsSensitiveKeywords(""));
        assertFalse(LogUtils.containsSensitiveKeywords(null));
        assertFalse(LogUtils.containsSensitiveKeywords("normal content"));
        
        assertTrue(LogUtils.containsSensitiveKeywords("user password is 123"));
        assertTrue(LogUtils.containsSensitiveKeywords("secret key"));
        assertTrue(LogUtils.containsSensitiveKeywords("用户密码"));
        
        log.info("敏感关键词检测测试完成");
    }

    @Test
    void testFormatSecurityLog() {
        // 测试安全日志格式化
        String result = LogUtils.formatSecurityLog("LOGIN", "SUCCESS", "用户登录成功");
        assertEquals("[SECURITY] LOGIN - SUCCESS | 用户登录成功", result);
        
        log.info("安全日志格式化测试完成");
    }

    @Test
    void testFormatAccessLog() {
        // 测试访问日志格式化
        String result = LogUtils.formatAccessLog("GET", "/api/test", "***********", "Mozilla/5.0", 100);
        assertTrue(result.contains("[ACCESS]"));
        assertTrue(result.contains("GET /api/test"));
        assertTrue(result.contains("Time: 100ms"));
        
        log.info("访问日志格式化测试完成");
    }

    @Test
    void testLogLevels() {
        // 测试不同日志级别
        log.trace("这是TRACE级别日志");
        log.debug("这是DEBUG级别日志");
        log.info("这是INFO级别日志");
        log.warn("这是WARN级别日志");
        log.error("这是ERROR级别日志");
        
        log.info("日志级别测试完成");
    }

    @Test
    void testSecurityLogging() {
        // 测试安全日志记录
        String username = "testuser";
        String ip = "***********00";
        String sessionId = "session123456789";
        
        // 使用脱敏工具记录安全日志
        log.info("安全事件 - 用户: {}, IP: {}, 会话: {}", 
                LogUtils.maskUsername(username),
                LogUtils.maskIpAddress(ip),
                LogUtils.maskSessionId(sessionId));
        
        // 测试敏感信息隐藏
        log.info("密码验证 - 结果: {}, 密码: {}", 
                "成功", 
                LogUtils.hideSensitiveInfo("actualPassword"));
        
        log.info("安全日志记录测试完成");
    }
}
