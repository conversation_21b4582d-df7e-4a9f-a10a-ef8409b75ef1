# 鉴权流程

所有Controller增加请求头

- Authorization  鉴权token
- X-Nonce  大型随机数
- X-Timestamp  毫米时间戳

## 操作流程

通过配置参数 `accessPassword`配置主密码, 

客户端每次请求时由客户端生成一个随机数 `X-Nonce` 和当前时间戳 ` X-Timestamp`设置到请求头, 客户端使用  `accessPassword `作为密钥对随机数+时间戳进行HmacSHA256签名, 设置到请求头 `Authorization `作为token,

服务端所有Controller增加过滤器, 对请求的鉴权参数进行验证, 

服务端先验证时间戳是否合法, 拦截和服务器时间误差超过±5分钟的请求, 服务端使用相同算法验证 `Authorization `是否合法, 并且将 `X-Nonce`使用caffeine缓存5分钟防止重放攻击
