package com.newnary.rpa.safebox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Safebox配置属性类
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 */
@Data
@Component
@ConfigurationProperties(prefix = "safebox")
public class SafeboxProperties {

    /**
     * 公钥
     **/
    private String ecPublicKey;

    /**
     * 私钥
     **/
    private String ecPrivateKey;

    /**
     * KeePass相关配置
     */
    private KeePass keepass;

    /**
     * 安全配置
     */
    private Security security;

    /**
     * 飞书机器人配置
     **/
    private LarkBot larkBot;

    /**
     * 接口访问密码
     **/
    private String accessPassword;

    /**
     * KeePass配置内部类
     */
    @Data
    public static class KeePass {
        /**
         * KeePass数据库文件路径
         */
        private String kdbxPath;

        /**
         * 密钥文件
         **/
        private String kdbxKey;

        /**
         * 最大文件大小（字节）
         */
        private long maxFileSize = 3 * 1024 * 1024; // 3MB

        /**
         * 允许的文件扩展名
         */
        private java.util.Set<String> allowedExtensions = java.util.Collections.singleton("kdbx");

    }

    /**
     * 安全配置内部类
     */
    @Data
    public static class Security {
        /**
         * 最大密码尝试次数
         */
        private int maxPasswordAttempts = 3;

        /**
         * 锁定持续时间（秒）
         */
        private int lockoutDuration = 300; // 5分钟

        /**
         * 密码申请会话超时(秒)
         **/
        private int applySessionTimeout = 30 * 60; // 30分钟

        /**
         * 最大密码申请会话数量
         **/
        private int applySessionMaximum = 50000;

    }

    /**
     * 飞书机器人
     **/
    @Data
    public static class LarkBot {
        /**
         * 飞书机器人appId
         **/
        private String appId;

        /**
         * 飞书机器人密钥
         **/
        private String appSecret;

        /**
         * 密码申请交互卡片ID
         **/
        private String securityApplyCardId;

        /**
         * 审核人员飞书openId
         **/
        private String processReceiveId;
    }

}