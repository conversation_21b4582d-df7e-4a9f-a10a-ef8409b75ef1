package com.newnary.rpa.safebox.keepass;

import lombok.extern.slf4j.Slf4j;
import org.linguafranca.pwdb.Credentials;
import org.linguafranca.pwdb.Entry;
import org.linguafranca.pwdb.PropertyValue;
import org.linguafranca.pwdb.kdbx.KdbxHeader;
import org.linguafranca.pwdb.kdbx.KdbxStreamFormat;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonEntry;
import org.linguafranca.pwdb.kdbx.jackson.JacksonGroup;
import org.linguafranca.pwdb.kdbx.jackson.JacksonSerializableDatabase;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * KeePass数据库管理服务
 * 提供KeePass数据库的创建、加载、保存、查询等功能
 *
 * <AUTHOR>
 * @since Created on 2025-09-09
 **/
@Slf4j
public class KeePassManager {

    /**
     * 创建空白的KeePass数据库
     * 使用V4默认参数创建空白数据库, AES, Argon2-d, ChaCha20
     *
     * @return 新创建的空白数据库
     */
    public JacksonDatabase createEmptyDatabase() {
        KdbxStreamFormat streamFormat = new KdbxStreamFormat(new KdbxHeader(4));
        try {
            return new JacksonDatabase(JacksonSerializableDatabase.createEmptyDatabase(), streamFormat);
        } catch (IOException e) {
            // 永不发生
            throw new RuntimeException("创建空白数据库失败", e);
        }
    }

    /**
     * 从文件路径加载KeePass数据库
     *
     * @param filePath 数据库文件路径
     * @param password 数据库密码
     * @return 加载的数据库对象
     * @throws IOException 文件读取异常
     */
    public JacksonDatabase load(String filePath, Credentials password) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new FileNotFoundException("KeePass数据库文件不存在: " + filePath);
        }

        try (InputStream inputStream = Files.newInputStream(path)) {
            return JacksonDatabase.load(password, inputStream);
        } catch (Exception e) {
            throw new IOException("加载KeePass数据库失败，可能是密码错误或文件损坏: " + e.getMessage(), e);
        }
    }

    /**
     * 保存KeePass数据库到指定文件
     *
     * @param database 要保存的数据库对象
     * @param filePath 保存的文件路径
     * @param password 数据库密码
     * @throws IOException 文件写入异常
     */
    public void save(JacksonDatabase database, String filePath, Credentials password) throws IOException {
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            database.save(password, outputStream);
        } catch (Exception e) {
            throw new IOException("保存KeePass数据库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据标题获取密码条目
     *
     * @param database 数据库对象
     * @param title    条目标题
     * @return 匹配的条目，如果未找到则返回Optional.empty()
     */
    public Optional<JacksonEntry> getEntryByTitle(JacksonDatabase database, String title) {
        if (database == null || title == null || title.trim().isEmpty()) {
            return Optional.empty();
        }

        JacksonGroup rootGroup = database.getRootGroup();
        return findEntryByTitle(rootGroup, title.trim());
    }

    /**
     * 创建新的密码条目
     *
     * @param database 数据库对象
     * @param title    条目标题
     * @param username 用户名
     * @param password 密码
     * @return 创建的条目对象
     */
    public JacksonEntry createEntry(JacksonDatabase database, String title, String username, PropertyValue password) {
        JacksonEntry entry = JacksonEntry.createEntry(database);
        entry.setTitle(title);
        entry.setUsername(username);
        entry.setPropertyValue(Entry.STANDARD_PROPERTY_NAME_PASSWORD, password);

        // 添加到根组
        JacksonGroup rootGroup = database.getRootGroup();
        rootGroup.addEntry(entry);

        return entry;
    }

    /**
     * 更新现有条目的信息
     *
     * @param entry    要更新的条目
     * @param title    新标题
     * @param username 新用户名
     * @param password 新密码
     */
    public void updateEntry(JacksonEntry entry, String title, String username, PropertyValue password) {
        if (entry == null) {
            throw new IllegalArgumentException("条目不能为空");
        }

        if (title != null) entry.setTitle(title);
        if (username != null) entry.setUsername(username);
        if (password != null) entry.setPropertyValue(Entry.STANDARD_PROPERTY_NAME_PASSWORD, password);
    }

    /**
     * 删除指定的条目
     *
     * @param database 数据库对象
     * @param entry    要删除的条目
     * @return 是否删除成功
     */
    public boolean deleteEntry(JacksonDatabase database, JacksonEntry entry) {
        if (database == null || entry == null) {
            return false;
        }

        JacksonGroup rootGroup = database.getRootGroup();
        return removeEntryFromGroup(rootGroup, entry);
    }

    /**
     * 获取数据库中所有条目
     *
     * @param database 数据库对象
     * @return 所有条目的列表
     */
    public List<JacksonEntry> getAllEntries(JacksonDatabase database) {
        List<JacksonEntry> allEntries = new ArrayList<>();
        if (database != null) {
            JacksonGroup rootGroup = database.getRootGroup();
            collectAllEntries(rootGroup, allEntries);
        }
        return allEntries;
    }

    /**
     * 根据关键词搜索条目
     *
     * @param database 数据库对象
     * @param keyword  搜索关键词
     * @return 匹配的条目列表
     */
    public List<JacksonEntry> searchEntries(JacksonDatabase database, String keyword) {
        if (database == null || keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return (List<JacksonEntry>) database.findEntries(keyword);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 在组中递归查找指定标题的条目
     *
     * @param group 要搜索的组
     * @param title 条目标题
     * @return 找到的条目，如果未找到则返回Optional.empty()
     */
    private static Optional<JacksonEntry> findEntryByTitle(JacksonGroup group, String title) {
        // 在当前组中查找
        for (JacksonEntry entry : group.getEntries()) {
            if (entry != null && entry.matchTitle(title)) {
                return Optional.of(entry);
            }
        }

        // 在子组中递归查找
        for (JacksonGroup subGroup : group.getGroups()) {
            if (subGroup != null) {
                Optional<JacksonEntry> found = findEntryByTitle(subGroup, title);
                if (found.isPresent()) {
                    return found;
                }
            }
        }

        return Optional.empty();
    }

    /**
     * 从组中递归删除指定条目
     *
     * @param group 要搜索的组
     * @param entry 要删除的条目
     * @return 是否删除成功
     */
    private static boolean removeEntryFromGroup(JacksonGroup group, JacksonEntry entry) {
        // 尝试从当前组中删除
        if (group.getEntries().remove(entry)) {
            return true;
        }

        // 在子组中递归删除
        for (JacksonGroup subGroup : group.getGroups()) {
            if (subGroup != null) {
                if (removeEntryFromGroup(subGroup, entry)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 递归收集组中的所有条目
     *
     * @param group   要收集的组
     * @param entries 收集结果列表
     */
    private static void collectAllEntries(JacksonGroup group, List<JacksonEntry> entries) {
        // 收集当前组中的条目
        for (JacksonEntry entry : group.getEntries()) {
            if (entry != null) {
                entries.add(entry);
            }
        }

        // 递归收集子组中的条目
        for (JacksonGroup subGroup : group.getGroups()) {
            if (subGroup != null) {
                collectAllEntries(subGroup, entries);
            }
        }
    }

    /**
     * 检查条目是否匹配搜索关键词
     *
     * @param entry   要检查的条目
     * @param keyword 搜索关键词（已转为小写）
     * @return 是否匹配
     */
    private static boolean matchesKeyword(JacksonEntry entry, String keyword) {
        if (entry == null || keyword == null) {
            return false;
        }

        return entry.match(keyword);
    }
}
